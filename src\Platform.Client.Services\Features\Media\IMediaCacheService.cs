namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Service for caching media files locally to improve performance and enable offline access
/// </summary>
public interface IMediaCacheService
{
    /// <summary>
    /// Gets cached image data by attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <returns>Cached image data or null if not found</returns>
    Task<byte[]?> GetCachedImageAsync(string attachmentId);

    /// <summary>
    /// Caches image data with the specified attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <param name="imageData">Image data to cache</param>
    /// <param name="mimeType">MIME type of the image</param>
    /// <returns>True if cached successfully, false otherwise</returns>
    Task<bool> CacheImageAsync(string attachmentId, byte[] imageData, string? mimeType = null);

    /// <summary>
    /// Gets cached thumbnail data by attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <returns>Cached thumbnail data or null if not found</returns>
    Task<byte[]?> GetCachedThumbnailAsync(string attachmentId);

    /// <summary>
    /// Caches thumbnail data with the specified attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <param name="thumbnailData">Thumbnail data to cache</param>
    /// <returns>True if cached successfully, false otherwise</returns>
    Task<bool> CacheThumbnailAsync(string attachmentId, byte[] thumbnailData);

    /// <summary>
    /// Removes cached media for the specified attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <returns>True if removed successfully, false if not found</returns>
    Task<bool> RemoveCachedMediaAsync(string attachmentId);

    /// <summary>
    /// Gets the total size of cached media in bytes
    /// </summary>
    /// <returns>Total cache size in bytes</returns>
    Task<long> GetCacheSizeAsync();

    /// <summary>
    /// Clears all cached media
    /// </summary>
    /// <returns>Number of files removed</returns>
    Task<int> ClearCacheAsync();

    /// <summary>
    /// Cleans up old cached files based on age and cache size limits
    /// </summary>
    /// <param name="maxCacheSizeBytes">Maximum cache size in bytes (default: 100MB)</param>
    /// <param name="maxAgeHours">Maximum age of cached files in hours (default: 168 hours = 7 days)</param>
    /// <returns>Number of files removed</returns>
    Task<int> CleanupCacheAsync(long maxCacheSizeBytes = 100_000_000, int maxAgeHours = 168);

    /// <summary>
    /// Checks if media is cached for the specified attachment ID
    /// </summary>
    /// <param name="attachmentId">Unique attachment identifier</param>
    /// <returns>True if cached, false otherwise</returns>
    Task<bool> IsCachedAsync(string attachmentId);

    /// <summary>
    /// Gets cache statistics
    /// </summary>
    /// <returns>Cache statistics including file count and total size</returns>
    Task<MediaCacheStats> GetCacheStatsAsync();
}

/// <summary>
/// Media cache statistics
/// </summary>
public class MediaCacheStats
{
    public int TotalFiles { get; set; }
    public long TotalSizeBytes { get; set; }
    public int ImageFiles { get; set; }
    public int ThumbnailFiles { get; set; }
    public DateTime? OldestFileDate { get; set; }
    public DateTime? NewestFileDate { get; set; }
}
