@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Components.Web
@inject ILogger<GlobalErrorBoundary> Logger
@inject IJSRuntime JSRuntime

<!-- Global Error Boundary - Nothing Phone Aesthetic -->
<ErrorBoundary @ref="errorBoundary">
    <ChildContent>
        @ChildContent
    </ChildContent>
    <ErrorContent Context="exception">
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            <div class="w-11/12 max-w-md mx-auto bg-dialog rounded-xl shadow-theme-lg border border-border overflow-hidden">
                
                <!-- Error Header -->
                <div class="bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 px-6 py-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">
                                Something went wrong
                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Error Content -->
                <div class="px-6 py-4">
                    <div class="text-sm text-gray-700 dark:text-gray-300 mb-4">
                        @if (IsProduction)
                        {
                            <p>We're sorry, but an unexpected error occurred. Please try again or refresh the page.</p>
                        }
                        else
                        {
                            <div class="space-y-2">
                                <p class="font-medium text-red-600 dark:text-red-400">Development Error Details:</p>
                                <div class="bg-gray-50 dark:bg-primary-800 rounded-lg p-3 text-xs font-mono overflow-auto max-h-32">
                                    <p class="text-red-600 dark:text-red-400 font-semibold">@exception.GetType().Name</p>
                                    <p class="text-gray-700 dark:text-gray-300 mt-1">@exception.Message</p>
                                    @if (!string.IsNullOrEmpty(exception.StackTrace))
                                    {
                                        <details class="mt-2">
                                            <summary class="cursor-pointer text-blue-600 dark:text-blue-400 hover:underline">Stack Trace</summary>
                                            <pre class="mt-2 text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">@exception.StackTrace</pre>
                                        </details>
                                    }
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Error ID for Support -->
                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-4 p-2 bg-gray-50 dark:bg-primary-800 rounded">
                        <span class="font-medium">Error ID:</span> @ErrorId
                        <br />
                        <span class="font-medium">Time:</span> @ErrorTime.ToString("yyyy-MM-dd HH:mm:ss")
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="bg-gray-50 dark:bg-primary-800 px-6 py-4 flex flex-col sm:flex-row gap-3">
                    <button @onclick="RetryOperation" 
                            class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Try Again
                    </button>
                    
                    <button @onclick="RefreshPage" 
                            class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>
    </ErrorContent>
</ErrorBoundary>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string? ComponentName { get; set; }
    [Parameter] public EventCallback OnRetry { get; set; }

    private ErrorBoundary? errorBoundary;
    private string ErrorId = Guid.NewGuid().ToString("N")[..8];
    private DateTime ErrorTime = DateTime.UtcNow;
    private bool IsProduction => Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "Development";

    protected override void OnParametersSet()
    {
        // Reset error boundary when parameters change
        errorBoundary?.Recover();
    }

    private async Task RetryOperation()
    {
        try
        {
            // Log retry attempt
            Logger.LogInformation("User attempting to retry operation. Error ID: {ErrorId}, Component: {ComponentName}", 
                ErrorId, ComponentName ?? "Unknown");

            // Reset error boundary
            errorBoundary?.Recover();

            // Call custom retry callback if provided
            if (OnRetry.HasDelegate)
            {
                await OnRetry.InvokeAsync();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during retry operation. Error ID: {ErrorId}", ErrorId);
        }
    }

    private async Task RefreshPage()
    {
        try
        {
            Logger.LogInformation("User refreshing page due to error. Error ID: {ErrorId}, Component: {ComponentName}", 
                ErrorId, ComponentName ?? "Unknown");

            await JSRuntime.InvokeVoidAsync("location.reload");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during page refresh. Error ID: {ErrorId}", ErrorId);
        }
    }

    public void HandleException(Exception exception)
    {
        // Generate new error ID for each exception
        ErrorId = Guid.NewGuid().ToString("N")[..8];
        ErrorTime = DateTime.UtcNow;

        // Log the exception with context
        Logger.LogError(exception, 
            "Unhandled exception in component: {ComponentName}. Error ID: {ErrorId}. " +
            "Exception Type: {ExceptionType}, Message: {Message}", 
            ComponentName ?? "Unknown", ErrorId, exception.GetType().Name, exception.Message);

        // Additional logging for specific exception types
        if (exception is JSException jsEx)
        {
            Logger.LogError("JavaScript exception details: {JSMessage}", jsEx.Message);
        }
        else if (exception is HttpRequestException httpEx)
        {
            Logger.LogError("HTTP request exception: {HttpMessage}", httpEx.Message);
        }
        else if (exception is TaskCanceledException)
        {
            Logger.LogWarning("Task was canceled - likely due to timeout or user navigation");
        }
    }
}
