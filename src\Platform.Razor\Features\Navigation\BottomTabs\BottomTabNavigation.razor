@using Microsoft.AspNetCore.Components.Authorization 
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider 
 
<!-- Bottom Tab Navigation - Nothing Phone Minimalistic Style -->
@if (showTabs && isAuthenticated)
{
    <nav class="fixed bottom-0 left-0 right-0 z-50 bg-nav border-t border-border safe-area-bottom shadow-theme-sm fade-in"
         aria-label="Main navigation"
         role="tablist">
        <!-- Tab Container -->
        <div class="flex items-center justify-around px-4 py-2">
            <!-- Messages Tab -->
            <button @onclick='() => NavigateToTab("/chat")'
                    class="@GetTabClasses("/chat") flex-1 flex flex-col items-center justify-center py-2 px-2 touch-target-lg transition-theme rounded-lg focus-ring-primary"
                    aria-label="Messages"
                    aria-describedby="@(unreadMessagesCount > 0 ? "messages-badge" : null)"
                    role="tab"
                    aria-selected="@IsActiveTab("/chat").ToString().ToLower()">
                <!-- Messages Icon -->
                <div class="@GetIconClasses("/chat") mb-1 relative">
                    @if (IsActiveTab("/chat"))
                    {
                        <img class="w-6 h-6" src="images/messages_solid.svg" />
                    }
                    else
                    { 
                        <img class="w-6 h-6" src="images/messages_light.svg" />
                    }
                    @if (unreadMessagesCount > 0)
                    {
                        <div id="messages-badge"
                             class="absolute -top-1 -right-1 bg-secondary-600 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm"
                             aria-label="@unreadMessagesCount unread messages"
                             role="status">
                            @(unreadMessagesCount > 99 ? "99+" : unreadMessagesCount.ToString())
                        </div>
                    }
                </div>
                <span class="@GetLabelClasses("/chat") text-xs font-medium leading-none">Messages</span>
            </button>

            <!-- Friends Tab -->
            <button @onclick='() => NavigateToTab("/friends")'
                    class="@GetTabClasses("/friends") flex-1 flex flex-col items-center justify-center py-2 px-2 touch-target-lg transition-theme rounded-lg focus-ring-primary"
                    aria-label="Friends"
                    aria-describedby="@(friendRequestsCount > 0 ? "friends-badge" : null)"
                    role="tab"
                    aria-selected="@IsActiveTab("/friends").ToString().ToLower()">
                <!-- Friends Icon -->
                <div class="@GetIconClasses("/friends") mb-1 relative">
                    @if (IsActiveTab("/friends"))
                    {
                        <img class="w-6 h-6" src="images/user_group_simple_solid.svg" />
                        
                    }
                    else
                    {

                        <img class="w-6 h-6" src="images/user_group_simple_light.svg" />
                    }
                    @if (friendRequestsCount > 0)
                    {
                        <div id="friends-badge"
                             class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-sm"
                             aria-label="@friendRequestsCount friend requests"
                             role="status">
                            @friendRequestsCount
                        </div>
                    }
                </div>
                <span class="@GetLabelClasses("/friends") text-xs font-medium leading-none">Friends</span>
            </button>

            <!-- Settings Tab -->
            <button @onclick='() => NavigateToTab("/settings")'
                    class="@GetTabClasses("/settings") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Settings">
                <!-- Settings Icon -->
                <div class="@GetIconClasses("/settings") mb-1">
                    @if (IsActiveTab("/settings"))
                    {
                        <img class="w-6 h-6" src="images/sliders_solid.svg" />
                    }
                    else
                    {
                        <img class="w-6 h-6" src="images/sliders_light.svg" />
                    }
                </div>
                <span class="@GetLabelClasses("/settings") text-xs font-medium leading-none">Settings</span>
            </button>

            <!-- Profile Tab -->
            <button @onclick='() => NavigateToTab("/profile")'
                    class="@GetTabClasses("/profile") flex-1 flex flex-col items-center justify-center py-2 px-2 min-h-[52px] transition-all duration-200 rounded-lg"
                    aria-label="Profile">
                <!-- Profile Icon -->
                <div class="@GetIconClasses("/profile") mb-1">
                    @if (IsActiveTab("/profile"))
                    {
                        <img class="w-6 h-6" src="images/user_gear_solid.svg" />
                    }
                    else
                    {
                        <img class="w-6 h-6" src="images/user_gear_light.svg" />
                    }
                </div>
                <span class="@GetLabelClasses("/profile") text-xs font-medium leading-none">Profile</span>
            </button>
        </div>
    </nav>
}
