﻿@using Platform.Framework.Core
@inherits FrameworkLayoutBaseComponent

<!-- Main Layout - WhatsApp Minimalistic Style with Nothing Phone Colors -->
<div class="flex flex-col h-screen bg-background text-primary">
    <!-- Main Content Area with Global Error Boundary -->
    <div class="flex-1 overflow-hidden">
        <Platform.Razor.Components.ErrorBoundary.GlobalErrorBoundary ComponentName="MainLayout">
            @Body
        </Platform.Razor.Components.ErrorBoundary.GlobalErrorBoundary>
    </div>
    @if (DialogService != null)
    {
        @foreach (var dialog in DialogService.Dialogs)
        {
            <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
                <!-- Backdrop with Nothing Phone aesthetic -->
                <div class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-theme"
                     aria-hidden="true" @onclick="() => CloseMe(dialog)"></div>

                <!-- Mobile-First Modal Container -->
                <div class="fixed inset-0 z-[150] flex items-end sm:items-center justify-center p-4 @dialog.DialogContainerClasses">
                    <!-- Optimized Modal Content Wrapper -->
                    <div class="mobile-dialog relative overflow-hidden rounded-xl bg-dialog shadow-theme-lg border border-border @dialog.SizeClasses">

                        <!-- Minimalistic Modal Header -->
                        <div class="bg-header border-b border-border flex items-center justify-between px-4 py-3 sm:px-6 sm:py-4">
                            <h3 class="truncate text-base font-semibold text-primary sm:text-lg">
                                @dialog.Title
                            </h3>
                            @if (dialog.ShowCrossIcon)
                            {
                                <button @onclick='() => CloseMe(dialog)' type="button"
                                        class="ml-auto flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-secondary hover:bg-muted hover:text-primary transition-theme focus-theme">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    <span class="sr-only">Close dialog</span>
                                </button>
                            }
                        </div>

                        <!-- Optimized Modal Body with Error Boundary -->
                        <div class="max-h-[calc(100vh-8rem)] sm:max-h-[calc(100vh-6rem)] overflow-y-auto bg-surface">
                            <Platform.Razor.Components.ErrorBoundary.DialogErrorBoundary
                                DialogTitle="@dialog.Title"
                                OnClose="() => CloseMe(dialog)"
                                OnRetry="() => StateHasChanged()">
                                <DynamicComponent Type="dialog.Component" Parameters="dialog.Parameters" />
                            </Platform.Razor.Components.ErrorBoundary.DialogErrorBoundary>
                        </div>
                    </div>
                </div>
            </div>
        }

    }
    <!-- Bottom Tab Navigation -->
    <Platform.Razor.Features.Navigation.BottomTabs.BottomTabNavigation />
</div>
 