﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Profile;
using System.ComponentModel.DataAnnotations;

namespace Platform.Client.Services.Features.Profile;

public class AvatarSelectionFormViewModel : ObservableBase
{
    /// <summary>
    /// ID of the selected image from the Images table
    /// </summary>
    [Required]
    public string? SelectedImageId { get; set; }

    /// <summary>
    /// Context for avatar selection: "profile" or "friend"
    /// </summary>
    [Required]
    public string Context { get; set; } = "profile";

    /// <summary>
    /// Target ID: userId for profile context, friendshipId for friend context
    /// </summary>
    [Required]
    public string? TargetId { get; set; }

    /// <summary>
    /// Available images for selection
    /// </summary>
    public List<AvailableImage>? AvailableImages { get; set; }

    /// <summary>
    /// Loading state for images
    /// </summary>
    public bool IsLoadingImages { get; set; }

    /// <summary>
    /// Success message after save
    /// </summary>
    public string? SuccessMessage { get; set; }
}
