﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.DataServices.Features.Profile;
public class AvatarSelectionServerSideListingDataService : ServerSideListingDataService<AvatarSelectionListingBusinessObject, AvatarSelectionFilterBusinessObject>, IAvatarSelectionListingDataService
{

	//private readonly AppDbContext _context;

	//public AvatarSelectionServerSideListingDataService (AppDbContext context)
	//{
		//_context = context;
	//}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public override IQueryable<AvatarSelectionListingBusinessObject> GetQuery(AvatarSelectionFilterBusinessObject filterBusinessObject)
	{
		throw new NotImplementedException();
	}
}
