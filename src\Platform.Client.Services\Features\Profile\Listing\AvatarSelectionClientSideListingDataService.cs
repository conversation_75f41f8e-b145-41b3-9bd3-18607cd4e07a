﻿using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.MauiShared;
using DeepMessage.MauiApp.Helpers;
using DeepMessage.ServiceContracts.Features.Profile;
namespace Platform.Client.Services.Features.Profile;
public class AvatarSelectionClientSideListingDataService : IAvatarSelectionListingDataService
{

	private readonly BaseHttpClient _httpClient;

	public AvatarSelectionClientSideListingDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<PagedDataList<AvatarSelectionListingBusinessObject>> GetPaginatedItems(AvatarSelectionFilterBusinessObject filterBusinessObject)
	{
		return await _httpClient.GetFromJsonAsync<PagedDataList<AvatarSelectionListingBusinessObject>>($"api/AvatarSelectionListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
	}
}
