using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Platform.Framework.Core;

namespace Platform.Razor.Features.Profile.Management
{
    public partial class ProfileManagement
    {
        [Inject] private NavigationManager Navigation { get; set; } = null!;
        [Inject] private IJSRuntime JsRuntime { get; set; } = null!;
        [Inject] private ILocalStorageService StorageService { get; set; } = null!;
     
        // Profile Information
        private string displayName = "John Doe";
        private string profilePictureUrl = "";

        // UI State
        private bool IsSaving = false;

        protected override async Task OnInitializedAsync()
        {
            await LoadProfile();
        }

        /// <summary>
        /// Loads profile data from storage
        /// </summary>
        private async Task LoadProfile()
        {
            try
            {
                // Load profile information
                var storedDisplayName = await StorageService.GetValue("display_name");
                if (!string.IsNullOrEmpty(storedDisplayName))
                {
                    displayName = storedDisplayName;
                }

                var storedProfilePicture = await StorageService.GetValue("profile_picture_url");
                if (!string.IsNullOrEmpty(storedProfilePicture))
                {
                    profilePictureUrl = storedProfilePicture;
                }
            }
            catch (Exception)
            {
                // Use default values if loading fails
            }
        }

        /// <summary>
        /// Saves profile data to storage
        /// </summary>
        private async Task SaveProfile()
        {
            if (IsSaving) return;

            try
            {
                IsSaving = true;
                StateHasChanged();

                // Save profile information
                await StorageService.SetValue(displayName, "display_name");
                await StorageService.SetValue(profilePictureUrl, "profile_picture_url");

                // Show success message
                await ShowToast("Profile saved successfully", "success");
            }
            catch (Exception ex)
            {
                await ShowToast($"Error saving profile: {ex.Message}", "error");
            }
            finally
            {
                IsSaving = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles profile picture upload success
        /// </summary>
        private async Task OnProfilePictureUploaded(string imageUrl)
        {
            profilePictureUrl = imageUrl;
            await ShowToast("Profile picture updated successfully", "success");
            StateHasChanged();
        }

        /// <summary>
        /// Handles profile picture upload error
        /// </summary>
        private async Task OnProfilePictureError(string errorMessage)
        {
            await ShowToast($"Failed to upload profile picture: {errorMessage}", "error");
        }

        /// <summary>
        /// Changes password
        /// </summary>
        private async Task ChangePassword()
        {
            Navigation.NavigateTo("/change-password");
        }

        /// <summary>
        /// Navigates to referral codes page
        /// </summary>
        private void NavigateToReferralCodes()
        {
            Navigation.NavigateTo("/referral-codes");
        }

        /// <summary>
        /// Gets initials from display name
        /// </summary>
        private string GetInitials(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "?";

            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
                return $"{parts[0][0]}{parts[1][0]}".ToUpper();
            
            return name[0].ToString().ToUpper();
        }

        /// <summary>
        /// Shows a toast message
        /// </summary>
        private async Task ShowToast(string message, string type = "info")
        {
            try
            {
                var bgColor = type switch
                {
                    "success" => "bg-green-600",
                    "error" => "bg-red-600",
                    "warning" => "bg-yellow-600",
                    _ => "bg-gray-800"
                };

                await JsRuntime.InvokeVoidAsync("eval", $@"
                    const toast = document.createElement('div');
                    toast.className = 'fixed top-4 right-4 {bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
                    toast.textContent = '{message}';
                    document.body.appendChild(toast);
                    setTimeout(() => {{
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(100%)';
                        setTimeout(() => toast.remove(), 300);
                    }}, 3000);
                ");
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
