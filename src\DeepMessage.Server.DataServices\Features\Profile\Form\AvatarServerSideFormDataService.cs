﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.Json;
using Azure.AI.OpenAI;
using Azure;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using OpenAI.Images;
using System.ComponentModel.DataAnnotations;

namespace DeepMessage.Server.DataServices.Features.Profile;

public class AvatarServerSideFormDataService : IAvatarFormDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AvatarServerSideFormDataService> _logger;

    public AvatarServerSideFormDataService(
        AppDbContext context,
        IHttpContextAccessor httpContextAccessor,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        ILogger<AvatarServerSideFormDataService> logger)
    {
        _context = context;
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _configuration = configuration;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(AvatarFormBusinessObject formBusinessObject)
    {
        try
        {
            var userId = _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            ArgumentNullException.ThrowIfNull(userId, "User not found");

            var user = await _userManager.FindByIdAsync(userId);
            ArgumentNullException.ThrowIfNull(user, "User not found in database");

            // Validate avatar description
            if (string.IsNullOrWhiteSpace(formBusinessObject.AvatarDescription))
            {
                throw new ArgumentException("Avatar description is required");
            }

            // Generate avatar using Azure OpenAI
            var avatarData = await GenerateAvatarAsync(formBusinessObject.AvatarDescription);

            // Update user's avatar data
            user.AvatarData = avatarData;
            var result = await _userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                throw new InvalidOperationException($"Failed to update user avatar: {errors}");
            }

            _logger.LogInformation("Avatar generated and saved for user {UserId}", userId);
            return "Avatar generated successfully";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating avatar for user");
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<AvatarFormBusinessObject?> GetItemByIdAsync(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return null;
            }

            return new AvatarFormBusinessObject
            {
                Id = user.Id,
                AvatarDescription = "Current avatar" // Placeholder since we don't store the original description
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving avatar data for user {UserId}", id);
            throw;
        }
    }

    private async Task<byte[]> GenerateAvatarAsync(string description)
    {
        try
        {
            // Get Azure OpenAI configuration
            var endpoint = _configuration["AzureOpenAI:Endpoint"];
            var apiKey = _configuration["AzureOpenAI:ApiKey"];
            var deploymentName = _configuration["AzureOpenAI:ImageDeploymentName"] ?? "dall-e-3";

            if (string.IsNullOrEmpty(endpoint) || string.IsNullOrEmpty(apiKey))
            {
                throw new InvalidOperationException("Azure OpenAI configuration is missing");
            }

            var client = new AzureOpenAIClient(new Uri(endpoint), new AzureKeyCredential(apiKey));

            // Enhance the prompt for better avatar generation
            var enhancedPrompt = $"Create a professional, friendly avatar portrait of a person with the following characteristics: {description}. " +
                               "The image should be suitable for a profile picture, with a clean background, good lighting, and professional appearance. " +
                               "Style should be realistic but polished, suitable for business or social media use.";

            var imageGenerationOptions = new ImageGenerationOptions()
            {
                EndUserId = _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value,
                Quality = GeneratedImageQuality.Standard,
                ResponseFormat = GeneratedImageFormat.Bytes,
                Size = GeneratedImageSize.W512xH512,
                Style = GeneratedImageStyle.Natural
            };

            _logger.LogInformation("Generating avatar with prompt: {Prompt}", enhancedPrompt);

            var imageClient = client.GetImageClient("");
            var response = await imageClient.GenerateImageAsync(enhancedPrompt);
            var imageData = response.Value;

            if (imageData?.ImageBytes == null)
            {
                throw new InvalidOperationException("Failed to generate avatar image");
            }

            _logger.LogInformation("Avatar generated successfully, size: {Size} bytes", imageData.ImageBytes);
            return imageData.ImageBytes.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Azure OpenAI for avatar generation");
            throw new InvalidOperationException("Failed to generate avatar using AI service", ex);
        }
    }
}
