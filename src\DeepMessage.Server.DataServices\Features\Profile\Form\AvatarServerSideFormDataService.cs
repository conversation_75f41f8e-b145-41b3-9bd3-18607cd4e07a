﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Text.Json;
using Azure.AI.OpenAI;
using Azure;
using OpenAI.Images;
using System.ComponentModel.DataAnnotations;
using OpenAI;
using System.Net.Http.Headers;
using System.Text;

namespace DeepMessage.Server.DataServices.Features.Profile;

public class AvatarServerSideFormDataService : IAvatarFormDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AvatarServerSideFormDataService> _logger;

    public AvatarServerSideFormDataService(
        AppDbContext context,
        IHttpContextAccessor httpContextAccessor,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        ILogger<AvatarServerSideFormDataService> logger)
    {
        _context = context;
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _configuration = configuration;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(AvatarFormBusinessObject formBusinessObject)
    {
        try
        {
            var userId = _httpContextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            ArgumentNullException.ThrowIfNull(userId, "User not found");

            var user = await _userManager.FindByIdAsync(userId);
            ArgumentNullException.ThrowIfNull(user, "User not found in database");

            if (formBusinessObject.Operation == "generate")
            {
                return await HandleGenerateOperation(formBusinessObject);
            }
            else if (formBusinessObject.Operation == "save")
            {
                return await HandleSaveOperation(formBusinessObject, user);
            }
            else
            {
                throw new ArgumentException($"Unknown operation: {formBusinessObject.Operation}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing avatar operation {Operation} for user", formBusinessObject.Operation);
            throw;
        }
    }

    private async Task<string> HandleGenerateOperation(AvatarFormBusinessObject formBusinessObject)
    {
        // Validate avatar description
        if (string.IsNullOrWhiteSpace(formBusinessObject.AvatarDescription))
        {
            throw new ArgumentException("Avatar description is required");
        }

        // Generate avatar using Azure OpenAI
        var avatarData = await GenerateAvatarAsync(formBusinessObject.AvatarDescription);
         

        _logger.LogInformation("Avatar generated successfully for user");
        return JsonSerializer.Serialize(new {
            success = true,
            message = "Avatar generated successfully",
            avatarData = avatarData
        });
    }

    private async Task<string> HandleSaveOperation(AvatarFormBusinessObject formBusinessObject, ApplicationUser user)
    {
        if (formBusinessObject.GeneratedAvatarData == null || formBusinessObject.GeneratedAvatarData.Length == 0)
        {
            throw new ArgumentException("No avatar data to save");
        }

        // Update user's avatar data
        user.AvatarData = formBusinessObject.GeneratedAvatarData;
        var result = await _userManager.UpdateAsync(user);

        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            throw new InvalidOperationException($"Failed to update user avatar: {errors}");
        }

        _logger.LogInformation("Avatar saved to database for user {UserId}", user.Id);
        return JsonSerializer.Serialize(new {
            success = true,
            message = "Avatar saved successfully"
        });
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<AvatarFormBusinessObject> GetItemByIdAsync(string id)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(id);

            return new AvatarFormBusinessObject
            {
                Id = user?.Id ?? id,
                AvatarDescription = string.Empty, // Reset for new generation
                GeneratedAvatarData = user?.AvatarData, // Include existing avatar if any
                Operation = "generate"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving avatar data for user {UserId}", id);
            throw;
        }
    }

    private async Task<string> GenerateAvatarAsync(string description)
    {
        try
        {
            // Get Azure OpenAI configuration
            var endpoint = _configuration["AzureOpenAI:Endpoint"];
            var apiKey = _configuration["AzureOpenAI:ApiKey"];
            var deploymentName = _configuration["AzureOpenAI:ImageDeploymentName"];

            var enhancedPrompt = $"Create an avatar picture with following characteristics: {description}";
            var payload = new
            {
                model = "dall-e-3",
                prompt = enhancedPrompt,
                size = "1024x1024",
                style = "vivid",
                quality = "standard",
                n = 1
            };

            var httpClient = new HttpClient();

            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var content = new StringContent(System.Text.Json.JsonSerializer.Serialize(payload), Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            Console.WriteLine($"Status: {response.StatusCode}");
            return responseContent;

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Azure OpenAI for avatar generation");
            throw new InvalidOperationException("Failed to generate avatar using AI service", ex);
        }
    }
}
