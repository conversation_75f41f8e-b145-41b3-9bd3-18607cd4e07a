﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.DataServices.Features.Profile;
public class AvatarServerSideFormDataService : IAvatarFormDataService
{

	private readonly AppDbContext _context;

	public AvatarServerSideFormDataService (AppDbContext context)
	{
		_context = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AvatarFormBusinessObject formBusinessObject)
	{
		throw new NotImplementedException();
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AvatarFormBusinessObject?> GetItemByIdAsync(string id)
	{
		throw new NotImplementedException();
	}
}
