using Microsoft.AspNetCore.Components;
using DeepMessage.ServiceContracts.Features.Profile;
using Platform.Client.Services.Features.Profile;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Platform.Razor.Features.Profile.Form
{
    public partial class AvatarForm
    {
   
        private async Task GenerateAvatar()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SelectedItem?.AvatarDescription))
                {
                    Error = "Please provide a description for your avatar";
                    return;
                }
                SelectedItem.Id = AuthenticatedUser.UserId;
                SelectedItem.IsGenerating = true;
                SelectedItem.HasGeneratedAvatar = false;
                SelectedItem.GeneratedAvatarData = null;
                Error = null;
                StateHasChanged();

                using var scope = ScopeFactory.CreateScope();
                var service = scope.ServiceProvider.GetRequiredService<IAvatarFormDataService>();

                var businessModel = new AvatarFormBusinessObject
                {
                    Id = SelectedItem.Id,
                    AvatarDescription = SelectedItem.AvatarDescription,
                    Operation = "generate"
                };

                var result = await service.SaveAsync(businessModel);

                // Parse the JSON response
                var response = JsonSerializer.Deserialize<JsonElement>(result);
                if (response.GetProperty("success").GetBoolean())
                {
                    var avatarDataBase64 = response.GetProperty("avatarData").GetString();
                    if (!string.IsNullOrEmpty(avatarDataBase64))
                    {
                        SelectedItem.GeneratedAvatarData = avatarDataBase64;
                        SelectedItem.HasGeneratedAvatar = true;
                        SelectedItem.SuccessMessage = "Avatar generated successfully! You can now save it or generate a new one.";
                    }
                }
                else
                {
                    Error = "Failed to generate avatar. Please try again.";
                }
            }
            catch (Exception ex)
            {
                Error = $"Error generating avatar: {ex.Message}";
                Logger.LogError(ex, "Error generating avatar");
            }
            finally
            {
                SelectedItem.IsGenerating = false;
                StateHasChanged();
            }
        }

        private async Task SaveAvatar()
        {
            try
            {
                if (SelectedItem?.GeneratedAvatarData == null)
                {
                    Error = "No avatar to save. Please generate an avatar first.";
                    return;
                }
                SelectedItem.Id = AuthenticatedUser.UserId;
                SelectedItem.IsSaving = true;
                Error = null;
                StateHasChanged();

                using var scope = ScopeFactory.CreateScope();
                var service = scope.ServiceProvider.GetRequiredService<IAvatarFormDataService>();

                var businessModel = new AvatarFormBusinessObject
                {
                    Id = SelectedItem.Id,
                    GeneratedAvatarData = SelectedItem.GeneratedAvatarData,
                    Operation = "save"
                };

                var result = await service.SaveAsync(businessModel);

                // Parse the JSON response
                var response = JsonSerializer.Deserialize<JsonElement>(result);
                if (response.GetProperty("success").GetBoolean())
                {
                    SelectedItem.SuccessMessage = "Avatar saved successfully!";

                    // Close the dialog after successful save
                    await Task.Delay(1500); // Show success message briefly
                    await CloseDialog();
                }
                else
                {
                    Error = "Failed to save avatar. Please try again.";
                }
            }
            catch (Exception ex)
            {
                Error = $"Error saving avatar: {ex.Message}";
                Logger.LogError(ex, "Error saving avatar");
            }
            finally
            {
                SelectedItem.IsSaving = false;
                StateHasChanged();
            }
        }

        private void GenerateNewAvatar()
        {
            SelectedItem.HasGeneratedAvatar = false;
            SelectedItem.GeneratedAvatarData = null;
            SelectedItem.SuccessMessage = null;
            Error = null;
            StateHasChanged();
        }

        private string GetAvatarDataUrl()
        {
            if (SelectedItem?.GeneratedAvatarData == null || SelectedItem.GeneratedAvatarData.Length == 0)
                return string.Empty;

            return $"data:image/png;base64,{SelectedItem.GeneratedAvatarData}";
        }

        public override async Task OnAfterSaveAsync(string key)
        {
            // Publish event to refresh profile
            PubSub.Hub.Default.Publish(new Tuple<string, string, dynamic>(OperationId, "Avatar Saved", key));
            await base.OnAfterSaveAsync(key);
        }
    }
}
