﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Profile;
namespace Platform.Client.Services.Features.Profile;
public class AvatarClientSideFormDataService : IAvatarFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public AvatarClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AvatarFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/AvatarsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AvatarFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<AvatarFormBusinessObject>($"api/AvatarsForm/GetItemById?id=" + id);
	}
}
