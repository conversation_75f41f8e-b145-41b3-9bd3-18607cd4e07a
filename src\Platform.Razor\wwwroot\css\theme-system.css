/* DeepMessage Theme System - Nothing Phone Aesthetic */
/* Comprehensive white theme for light mode with dark mode preparation */

:root {
  /* Nothing Phone Color Palette - Light Mode */
  --dm-white: #ffffff;
  --dm-gray-50: #fafafa;
  --dm-gray-100: #f5f5f5;
  --dm-gray-200: #eeeeee;
  --dm-gray-300: #e0e0e0;
  --dm-gray-400: #bdbdbd;
  --dm-gray-500: #9e9e9e;
  --dm-gray-600: #757575;
  --dm-gray-700: #424242;
  --dm-gray-800: #2c2c2c;
  --dm-gray-900: #1a1a1a;
  --dm-black: #000000;

  /* Nothing Phone Red Accent */
  --dm-red-50: #fef2f2;
  --dm-red-100: #fee2e2;
  --dm-red-200: #fecaca;
  --dm-red-300: #fca5a5;
  --dm-red-400: #f87171;
  --dm-red-500: #ef4444;
  --dm-red-600: #dc2626;
  --dm-red-700: #b91c1c;
  --dm-red-800: #991b1b;
  --dm-red-900: #7f1d1d;

  /* Semantic Color Tokens - Light Mode */
  --dm-bg-primary: var(--dm-white);
  --dm-bg-secondary: var(--dm-gray-50);
  --dm-bg-tertiary: var(--dm-gray-100);
  --dm-bg-surface: var(--dm-white);
  --dm-bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors - Light Mode */
  --dm-text-primary: var(--dm-gray-900);
  --dm-text-secondary: var(--dm-gray-600);
  --dm-text-tertiary: var(--dm-gray-500);
  --dm-text-inverse: var(--dm-white);
  --dm-text-accent: var(--dm-red-600);

  /* Border Colors - Light Mode */
  --dm-border-primary: var(--dm-gray-200);
  --dm-border-secondary: var(--dm-gray-300);
  --dm-border-focus: var(--dm-red-500);

  /* Component Specific Colors - Light Mode */
  --dm-header-bg: var(--dm-white);
  --dm-header-text: var(--dm-gray-900);
  --dm-header-border: var(--dm-gray-200);

  --dm-nav-bg: var(--dm-white);
  --dm-nav-text: var(--dm-gray-600);
  --dm-nav-text-active: var(--dm-gray-900);
  --dm-nav-border: var(--dm-gray-200);

  --dm-chat-bg: var(--dm-gray-50);
  --dm-chat-bubble-sent: var(--dm-gray-200);
  --dm-chat-bubble-received: var(--dm-white);
  --dm-chat-input-bg: var(--dm-white);

  --dm-dialog-bg: var(--dm-white);
  --dm-dialog-header-bg: var(--dm-white);
  --dm-dialog-border: var(--dm-gray-200);

  /* Shadows */
  --dm-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --dm-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --dm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark Mode Preparation */
@media (prefers-color-scheme: dark) {
  :root {
    /* Background Colors - Dark Mode */
    --dm-bg-primary: var(--dm-gray-900);
    --dm-bg-secondary: var(--dm-gray-800);
    --dm-bg-tertiary: var(--dm-gray-700);
    --dm-bg-surface: var(--dm-gray-800);
    --dm-bg-overlay: rgba(255, 255, 255, 0.1);

    /* Text Colors - Dark Mode */
    --dm-text-primary: var(--dm-white);
    --dm-text-secondary: var(--dm-gray-300);
    --dm-text-tertiary: var(--dm-gray-400);
    --dm-text-inverse: var(--dm-gray-900);
    --dm-text-accent: var(--dm-red-400);

    /* Border Colors - Dark Mode */
    --dm-border-primary: var(--dm-gray-700);
    --dm-border-secondary: var(--dm-gray-600);
    --dm-border-focus: var(--dm-red-400);

    /* Component Specific Colors - Dark Mode */
    --dm-header-bg: var(--dm-gray-900);
    --dm-header-text: var(--dm-white);
    --dm-header-border: var(--dm-gray-700);

    --dm-nav-bg: var(--dm-gray-900);
    --dm-nav-text: var(--dm-gray-400);
    --dm-nav-text-active: var(--dm-white);
    --dm-nav-border: var(--dm-gray-700);

    --dm-chat-bg: var(--dm-gray-800);
    --dm-chat-bubble-sent: var(--dm-gray-700);
    --dm-chat-bubble-received: var(--dm-gray-600);
    --dm-chat-input-bg: var(--dm-gray-700);

    --dm-dialog-bg: var(--dm-gray-800);
    --dm-dialog-header-bg: var(--dm-gray-800);
    --dm-dialog-border: var(--dm-gray-700);

    /* Shadows - Dark Mode */
    --dm-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --dm-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --dm-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  }
}

/* Explicit Dark Mode Class Support */
.dark {
  /* Background Colors - Dark Mode */
  --dm-bg-primary: var(--dm-gray-900);
  --dm-bg-secondary: var(--dm-gray-800);
  --dm-bg-tertiary: var(--dm-gray-700);
  --dm-bg-surface: var(--dm-gray-800);
  --dm-bg-overlay: rgba(255, 255, 255, 0.1);

  /* Text Colors - Dark Mode */
  --dm-text-primary: var(--dm-white);
  --dm-text-secondary: var(--dm-gray-300);
  --dm-text-tertiary: var(--dm-gray-400);
  --dm-text-inverse: var(--dm-gray-900);
  --dm-text-accent: var(--dm-red-400);

  /* Border Colors - Dark Mode */
  --dm-border-primary: var(--dm-gray-700);
  --dm-border-secondary: var(--dm-gray-600);
  --dm-border-focus: var(--dm-red-400);

  /* Component Specific Colors - Dark Mode */
  --dm-header-bg: var(--dm-gray-900);
  --dm-header-text: var(--dm-white);
  --dm-header-border: var(--dm-gray-700);

  --dm-nav-bg: var(--dm-gray-900);
  --dm-nav-text: var(--dm-gray-400);
  --dm-nav-text-active: var(--dm-white);
  --dm-nav-border: var(--dm-gray-700);

  --dm-chat-bg: var(--dm-gray-800);
  --dm-chat-bubble-sent: var(--dm-gray-700);
  --dm-chat-bubble-received: var(--dm-gray-600);
  --dm-chat-input-bg: var(--dm-gray-700);

  --dm-dialog-bg: var(--dm-gray-800);
  --dm-dialog-header-bg: var(--dm-gray-800);
  --dm-dialog-border: var(--dm-gray-700);
}

/* Enhanced Tailwind Integration Classes */
/* These extend Tailwind's capabilities with our theme system */

/* Custom Shadow Utilities (integrated with Tailwind) */
.shadow-theme-sm { box-shadow: var(--dm-shadow-sm); }
.shadow-theme-md { box-shadow: var(--dm-shadow-md); }
.shadow-theme-lg { box-shadow: var(--dm-shadow-lg); }

/* Enhanced Focus States for Theme System */
.focus-theme:focus {
  outline: 2px solid var(--dm-border-focus);
  outline-offset: 2px;
}

/* Smooth Transitions for Theme Switching */
.transition-theme {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-theme-all {
  transition: all 0.2s ease-in-out;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .dm-transition,
  .dm-transition-colors {
    transition: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --dm-border-primary: var(--dm-gray-400);
    --dm-border-secondary: var(--dm-gray-500);
  }
  
  .dark {
    --dm-border-primary: var(--dm-gray-500);
    --dm-border-secondary: var(--dm-gray-400);
  }
}
