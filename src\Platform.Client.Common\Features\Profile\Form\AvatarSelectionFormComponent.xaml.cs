﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Profile;
using Platform.Client.Services.Features.Profile;
namespace Platform.Client.Common.Features.Profile;
public class AvatarSelectionFormViewBase : FormBaseMaui<AvatarSelectionFormBusinessObject, AvatarSelectionFormViewModel, string, IAvatarSelectionFormDataService>
{
    public AvatarSelectionFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class AvatarSelectionFormView : AvatarSelectionFormViewBase
{
    public AvatarSelectionFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
