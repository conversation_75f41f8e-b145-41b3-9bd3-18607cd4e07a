﻿@inherits ListingBase<AvatarSelectionListingViewModel,AvatarSelectionListingBusinessObject,AvatarSelectionFilterViewModel,AvatarSelectionFilterBusinessObject, IAvatarSelectionListingDataService>
@using DeepMessage.ServiceContracts.Features.Profile
@using Platform.Client.Services.Features.Profile

<div class="listing-container">
    @if (IsWorking)
    {
        <div class="loading">Loading...</div>
    }
    else if (!string.IsNullOrEmpty(Error))
    {
        <div class="error">@Error</div>
    }
    else
    {
        <div class="items">
            @foreach (var item in Items)
            {
                <div class="item">
                    <!-- Add your listing item template here -->
                    @item
                </div>
            }
        </div>
        
        @if (UsePagination)
        {
            <div class="pagination">
                <button @onclick="PreviousPage" disabled="@(CurrentPage <= 1)">Previous</button>
                <span>Page @CurrentPage of @Math.Ceiling((double)TotalRecords / PageSize)</span>
                <button @onclick="NextPage" disabled="@(CurrentPage >= Math.Ceiling((double)TotalRecords / PageSize))">Next</button>
            </div>
        }
    }
</div>
