﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.WebApis.Controller.Profile;
[ApiController, Route("{culture:culture}/api/[controller]/[action]")]
public class AvatarsFormController : ControllerBase, IAvatarFormDataService
{

	private readonly IAvatarFormDataService dataService;

	public AvatarsFormController(IAvatarFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] AvatarFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<AvatarFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
