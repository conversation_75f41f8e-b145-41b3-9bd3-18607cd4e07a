# Framework Implementation Rules & Guidelines

## Table of Contents
1. [Core Principles](#core-principles)
2. [Vertical Slice Architecture](#vertical-slice-architecture)
3. [Service Interface Patterns](#service-interface-patterns)
4. [Implementation Types](#implementation-types)
5. [Dependency Injection Patterns](#dependency-injection-patterns)
6. [Controller Guidelines](#controller-guidelines)
7. [Authentication Implementation](#authentication-implementation)
8. [Development Rules](#development-rules)

## Core Principles

### Fundamental Concept
**Every complex UI can be broken down into simple lists and simple forms.**

- Complex modules (e.g., profile module) consist of independent sections:
  - Personal information section
  - Addresses section
  - Phone numbers section
- Each section is an independent **vertical slice**
- Each slice implements either `IListingDataService<T>` or `IFormDataService<T>`

### Cross-Platform Support
The framework supports multiple application types from the same codebase:
- Web applications
- Mobile applications
- Single Page Applications (SPA)
- WebAssembly clients
- Server-side rendered applications

## Vertical Slice Architecture

### Definition
A **vertical slice** is a self-contained feature that includes:
- Data access layer
- Business logic
- API controller
- UI component
- All necessary services

### Slice Types
1. **Listing Slices**: Display one or more records
2. **Form Slices**: Handle data input and submission

## Service Interface Patterns

### IListingDataService&lt;T&gt;
Used for:
- Displaying lists of records
- Read-only data display
- Single record views (e.g., personal information read-only view)

**Key Methods:**
- `GetPaginatedItems()` - Main method for data retrieval
- Inherits pagination and caching support from generic base service

### IFormDataService&lt;T&gt;
Used for:
- Data input forms
- Data submission and validation
- CRUD operations

## Implementation Types

### 1. Server-Side Implementation
**Purpose**: Primary data access from database

**Characteristics:**
- Direct database access through DbContext
- Access to cloud storage constructs
- Redis cache integration
- Other service constructs
- Implements actual query logic in `GetQuery()` method
- Inherits materialization logic, pagination, and caching from generic base

**Example:**
```csharp
public class PersonalInformationServerSideService : IPersonalInformationListingDataService
{
    // Implements GetQuery() method with actual database query
    // Inherits pagination and caching from generic server-side service
}
```

### 2. Client-Side Implementation
**Purpose**: Online data interactions via HTTP

**Characteristics:**
- Uses HttpClient for API communication
- Calls controller endpoints
- Used when component runs in client-side context

**Example:**
```csharp
public class PersonalInformationClientDataService : IPersonalInformationListingDataService
{
    // Implements HTTP client calls to controller endpoints
}
```

### 3. Offline Implementation
**Purpose**: Local data access when offline

**Characteristics:**
- Queries local database
- Works without internet connection
- Provides fallback functionality

**Example:**
```csharp
public class PersonalInformationOfflineDataService : IPersonalInformationListingDataService
{
    // Implements local database queries
}
```

## Dependency Injection Patterns

### Single Implementation
For features requiring only one implementation:
```csharp
services.AddScoped<IPersonalInformationListingDataService, PersonalInformationServerSideService>();
```

### Offline/Online Fallback Pattern
For features requiring offline support with online backup:

```csharp
// Default (offline) implementation
services.AddScoped<ISignInFormDataService, SignInLocalFormDataService>();

// Backup (online) implementation as keyed service
services.AddKeyedScoped<ISignInFormDataService, SignInClientFormDataService>("online");
```

### Service Resolution
- **Server-side context**: Server-side implementation injected
- **Client-side context**: Client-side implementation injected
- **Offline scenario**: Default service used, with manual fallback to keyed service

## Controller Guidelines

### One Service Per Controller Rule
**CRITICAL**: Always expose one service through one controller. Do not bloat controllers.

### Controller Implementation
```csharp
public class PersonalInformationController : ControllerBase, IPersonalInformationListingDataService
{
    private readonly IPersonalInformationListingDataService _service;
    
    // Exposes GetPaginatedItems() method
    // Stays within IListingDataService methods only
}
```

### Method Restrictions
- Stay within methods provided by `IListingDataService<T>` and `IFormDataService<T>`
- Keep slices thin
- Maintain separation of concerns
- Use established patterns to fulfill requirements within interface constraints

## Authentication Implementation

### Sign Up
- **Online only**: Single implementation (`SignUpClientFormDataService`)
- No offline support needed

### Sign In
- **Hybrid approach**: Offline-first with online fallback

#### Local Authentication Flow
1. Generate AES key from password
2. Encrypt username with password-derived key
3. Match against locally stored encrypted username
4. If match: Authentication successful
5. If no match: Fall back to online authentication

#### Online Authentication Flow (Backup)
1. Contact server for new devices
2. Perform server-side authentication
3. Retrieve encrypted username
4. Store locally for future offline use
5. Generate token for subsequent API calls

#### Implementation Pattern
```csharp
// Default (local) service
services.AddScoped<ISignInFormDataService, SignInLocalFormDataService>();

// Backup (online) service
services.AddKeyedScoped<ISignInFormDataService, SignInClientFormDataService>("online");
```

## Development Rules

### 1. Interface Compliance
- Every slice MUST implement either `IListingDataService<T>` or `IFormDataService<T>`
- No exceptions to this rule

### 2. Controller Constraints
- One service per controller
- Stay within interface methods
- No additional methods beyond interface requirements

### 3. Service Naming Convention
- `[Feature][ServerSide|Client|Offline][DataService|FormDataService]`
- Examples:
  - `PersonalInformationServerSideDataService`
  - `SignInClientFormDataService`
  - `ContactsOfflineDataService`

### 4. Dependency Registration
- Default implementation: `services.AddScoped<Interface, Implementation>()`
- Backup implementation: `services.AddKeyedScoped<Interface, Implementation>("key")`

### 5. Razor Component Integration
- Components declare dependency on interface, not concrete implementation
- Polymorphism handles correct implementation injection
- Manual fallback logic for keyed services when needed

### 6. Security Considerations
- Client-side encryption for sensitive data
- Password-derived AES keys
- Public/private key cryptography
- Local storage of encrypted credentials
- Token-based authentication for online scenarios

### 7. Future Framework Enhancements
- Framework may be enhanced to automatically support dual implementations
- Current manual fallback approach will be maintained for backward compatibility

## Best Practices

1. **Keep slices thin and focused**
2. **Maintain clear separation of concerns**
3. **Use established patterns consistently**
4. **Implement offline-first approach where applicable**
5. **Follow naming conventions strictly**
6. **Document any deviations from standard patterns**
7. **Test both online and offline scenarios**
8. **Ensure proper error handling in fallback scenarios**

---

*This document serves as the definitive guide for implementing features within our vertical slice architecture framework. All team members and coding agents must follow these rules to ensure consistency and maintainability.*
