@using Microsoft.AspNetCore.Components
@using Platform.Client.Services.Features.Media
@using DeepMessage.ServiceContracts.Features.Conversation
@inject IImageCompressionService ImageCompressionService
@inject IJSRuntime JSRuntime

@* Media Capture Component for Camera and Gallery Access *@
<div class="media-capture-container">
    @if (IsSupported)
    {
        <div class="flex gap-2">
            <!-- Camera Button -->
            <button type="button" @onclick="CapturePhoto" 
                    disabled="@IsCapturing"
                    class="media-capture-btn camera-btn">
                @if (IsCapturing && CaptureMode == "camera")
                {
                    <div class="loading-spinner"></div>
                }
                else
                {
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                }
            </button>

            <!-- Gallery Button -->
            <button type="button" @onclick="SelectPhoto" 
                    disabled="@IsCapturing"
                    class="media-capture-btn gallery-btn">
                @if (IsCapturing && CaptureMode == "gallery")
                {
                    <div class="loading-spinner"></div>
                }
                else
                {
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                }
            </button>
        </div>
    }
    else
    {
        <!-- Fallback for unsupported platforms -->
        <div class="text-gray-500 text-sm">
            Media capture not available on this platform
        </div>
    }

    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="error-message">
            @ErrorMessage
        </div>
    }
</div>

<style>
    .media-capture-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .media-capture-btn {
        padding: 0.5rem;
        border-radius: 9999px;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border: 1px solid #d1d5db;
        background-color: white;
        color: #4b5563;
        cursor: pointer;
    }

    .media-capture-btn:hover:not(:disabled) {
        border-color: #9ca3af;
        background-color: #f9fafb;
        color: #1f2937;
    }

    .media-capture-btn:disabled {
        background-color: #f3f4f6;
        cursor: not-allowed;
        color: #9ca3af;
    }

    .camera-btn:hover:not(:disabled) {
        background-color: #eff6ff;
        border-color: #93c5fd;
        color: #2563eb;
    }

    .gallery-btn:hover:not(:disabled) {
        background-color: #f0fdf4;
        border-color: #86efac;
        color: #16a34a;
    }

    .loading-spinner {
        width: 1rem;
        height: 1rem;
        border: 2px solid #d1d5db;
        border-top-color: #2563eb;
        border-radius: 50%;
    }

    .error-message {
        color: #dc2626;
        font-size: 0.875rem;
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.375rem;
        padding: 0.5rem;
    }

    /* Dark mode adjustments - simplified for Razor compatibility */
</style>

@code {
    [Parameter] public EventCallback<MediaCaptureResult> OnMediaCaptured { get; set; }
    [Parameter] public EventCallback<string> OnError { get; set; }

    private bool IsCapturing { get; set; } = false;
    private string CaptureMode { get; set; } = "";
    private string ErrorMessage { get; set; } = "";

    /// <summary>
    /// Check if media capture is supported on current platform
    /// This will be determined at runtime by the MAUI host
    /// </summary>
    private bool IsSupported => true; // Will be handled by platform-specific implementations

    /// <summary>
    /// Capture photo using device camera
    /// </summary>
    private async Task CapturePhoto()
    {
        if (IsCapturing) return;

        try
        {
            IsCapturing = true;
            CaptureMode = "camera";
            ErrorMessage = "";
            StateHasChanged();

            // Call JavaScript interop to trigger platform-specific camera capture
            var result = await JSRuntime.InvokeAsync<string>("capturePhoto");

            if (!string.IsNullOrEmpty(result))
            {
                await ProcessCapturedMedia(result, "camera");
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error capturing photo: {ex.Message}";
            await OnError.InvokeAsync(ErrorMessage);
        }
        finally
        {
            IsCapturing = false;
            CaptureMode = "";
            StateHasChanged();
        }
    }

    /// <summary>
    /// Select photo from device gallery
    /// </summary>
    private async Task SelectPhoto()
    {
        if (IsCapturing) return;

        try
        {
            IsCapturing = true;
            CaptureMode = "gallery";
            ErrorMessage = "";
            StateHasChanged();

            // Call JavaScript interop to trigger platform-specific photo picker
            var result = await JSRuntime.InvokeAsync<string>("selectPhoto");

            if (!string.IsNullOrEmpty(result))
            {
                await ProcessCapturedMedia(result, "gallery");
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error selecting photo: {ex.Message}";
            await OnError.InvokeAsync(ErrorMessage);
        }
        finally
        {
            IsCapturing = false;
            CaptureMode = "";
            StateHasChanged();
        }
    }

    /// <summary>
    /// Process the captured media data (base64 string from JavaScript)
    /// </summary>
    private async Task ProcessCapturedMedia(string base64Data, string source)
    {
        try
        {
            // Remove data URL prefix if present
            if (base64Data.StartsWith("data:"))
            {
                var commaIndex = base64Data.IndexOf(',');
                if (commaIndex > 0)
                {
                    base64Data = base64Data.Substring(commaIndex + 1);
                }
            }

            var imageData = Convert.FromBase64String(base64Data);

            // Validate image
            if (!ImageCompressionService.IsValidImage(imageData))
            {
                throw new InvalidOperationException("Selected file is not a valid image");
            }

            // Get original dimensions
            var (width, height) = await ImageCompressionService.GetImageDimensionsAsync(imageData);

            // Compress image
            var compressedData = await ImageCompressionService.CompressImageAsync(imageData);

            // Generate thumbnail
            var thumbnailData = await ImageCompressionService.GenerateThumbnailAsync(compressedData);

            var result = new MediaCaptureResult
            {
                FileName = $"{source}_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                OriginalData = imageData,
                CompressedData = compressedData,
                ThumbnailData = thumbnailData,
                MimeType = "image/jpeg",
                OriginalSizeBytes = imageData.Length,
                CompressedSizeBytes = compressedData.Length,
                OriginalWidth = width,
                OriginalHeight = height
            };

            await OnMediaCaptured.InvokeAsync(result);
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error processing captured media: {ex.Message}";
            await OnError.InvokeAsync(ErrorMessage);
        }
    }
}
