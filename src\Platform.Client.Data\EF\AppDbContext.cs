﻿using Microsoft.EntityFrameworkCore;
using Platform.Client.Data.EF;

namespace DeepMessage.Client.Common.Data
{
    public class AppDbContext  : DbContext
    {
        public DbSet<ApplicationUser> ApplicationUsers  => Set<ApplicationUser>();
        public DbSet<Conversation> Conversations => Set<Conversation>();
        public DbSet<ConversationParticipant> ConversationParticipants => Set<ConversationParticipant>();
        public DbSet<Message> Messages => Set<Message>();
        public DbSet<MessageRecipient> MessageRecipients => Set<MessageRecipient>();
        public DbSet<MessageAttachment> MessageAttachments => Set<MessageAttachment>();

        public DbSet<Friendship> Friendships => Set<Friendship>();

        public DbSet<NewsItem> NewsItems => Set<NewsItem>();

        public AppDbContext(DbContextOptions<AppDbContext> options)
       : base(options)
        {
           
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
             
            // Message -> MessageRecipient (one-to-many)
            modelBuilder.Entity<MessageRecipient>()
                .HasOne(mr => mr.Message)
                .WithMany(m => m.Recipients)
                .HasForeignKey(mr => mr.MessageId);

            // Conversation -> Messages (one-to-many)
            modelBuilder.Entity<Message>()
                .HasOne(m => m.Conversation)
                .WithMany(c => c.Messages)
                .HasForeignKey(m => m.ConversationId);

            // Message -> Attachments (one-to-many)
            modelBuilder.Entity<MessageAttachment>()
                .HasOne(ma => ma.Message)
                .WithMany(m => m.Attachments)
                .HasForeignKey(ma => ma.MessageId);
             

            // Participant relationship
            modelBuilder.Entity<ConversationParticipant>()
                .HasOne(cp => cp.Conversation)
                .WithMany(c => c.Participants)
                .HasForeignKey(cp => cp.ConversationId);

            // Prevent duplicate participants in a conversation:
            modelBuilder.Entity<ConversationParticipant>()
                .HasIndex(cp => new { cp.ConversationId, cp.UserId })
                .IsUnique();

            // Likewise, you might want a unique constraint on 
            // (MessageId, RecipientId) in MessageRecipient, 
            // so you don't accidentally insert duplicates:
            modelBuilder.Entity<MessageRecipient>()
                .HasIndex(mr => new { mr.MessageId, mr.RecipientId })
                .IsUnique(); 
              
            // If you want a unique (UserId, FriendId) so you don't duplicate:
            modelBuilder.Entity<Friendship>()
                .HasIndex(f => new { f.UserId, f.FriendId })
                .IsUnique();
        }
    }

   
}
