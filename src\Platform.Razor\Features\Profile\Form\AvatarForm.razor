﻿@inherits FormBase<AvatarFormBusinessObject,AvatarFormViewModel, string, IAvatarFormDataService>
@using DeepMessage.ServiceContracts.Features.Profile
@using Platform.Client.Services.Features.Profile
@using Platform.Framework.Core

<div class="bg-white min-h-screen p-4">
    <div class="max-w-md mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">Generate Avatar</h1>
            <p class="text-gray-600 text-sm">Describe your ideal avatar and we'll create it for you using AI</p>
        </div>

        @if (IsWorking)
        {
            <!-- Loading State -->
            <div class="flex flex-col items-center justify-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mb-4"></div>
                <p class="text-gray-600 text-center">Generating your avatar...</p>
                <p class="text-gray-500 text-sm text-center mt-2">This may take a few moments</p>
            </div>
        }
        else if (!string.IsNullOrEmpty(Error))
        {
            <!-- Error State -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-red-800 text-sm font-medium">Error generating avatar</span>
                </div>
                <p class="text-red-700 text-sm mt-1">@Error</p>
            </div>
        } 

        <!-- Form -->
        <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit" class="space-y-6">
            <DataAnnotationsValidator />

            <!-- Avatar Description Input -->
            <div class="space-y-2">

                <Platform.Razor.Components.Avatar.AvatarDisplay AvatarData="@SelectedItem.AvatarData" />

                <label for="avatar-description" class="block text-sm font-medium text-gray-900">
                    Describe Your Avatar
                </label>
                <div class="relative">
                    <InputTextArea
                        id="avatar-description"
                        @bind-Value="SelectedItem.AvatarDescription"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-transparent resize-none text-gray-900 placeholder-gray-500"
                        placeholder="e.g., A professional woman with short brown hair, wearing a blue blazer, smiling confidently..."
                        rows="4"
                        disabled="@IsWorking" />
                </div>
                <ValidationMessage For="@(() => SelectedItem.AvatarDescription)" class="text-red-600 text-sm" />

                <!-- Helper Text -->
                <div class="text-xs text-gray-500 space-y-1">
                    <p>• Be specific about appearance, clothing, and style</p>
                    <p>• Mention hair color, facial features, and expression</p>
                    <p>• Include professional or casual attire preferences</p>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button
                    type="submit"
                    class="w-full bg-gray-900 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px]"
                    disabled="@(IsWorking || string.IsNullOrWhiteSpace(SelectedItem?.AvatarDescription))">
                    @if (IsWorking)
                    {
                        <span class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating Avatar...
                        </span>
                    }
                    else
                    {
                        <span>Generate Avatar</span>
                    }
                </button>
            </div>
        </EditForm>
         
    </div>
</div>
