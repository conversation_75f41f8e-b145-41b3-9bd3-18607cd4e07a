using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using System;
using System.Security.Claims;
using System.Text.Json;

namespace Platform.Razor.Features.Authentication.Captcha
{
    public partial class FakeCaptchaScreen
    {
        public string? NickName { get; set; }


        [Inject]
        IClientEncryptionService _encryptionService { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            StateHasChanged();

            // Small delay to ensure smooth loading transition
            await Task.Delay(100);

            // Load nickname to determine display state
            NickName = await StorageService.GetValue(ClaimTypes.Name);

            StateHasChanged();

            await base.OnInitializedAsync();

        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && !IsWorking)
            {
                // Auto-focus the input field and show keyboard only after loading is complete
                await Task.Delay(300); // Slightly longer delay to ensure smooth transition
                try
                {
                    await JsRuntime.InvokeVoidAsync("eval", "document.getElementById('authCode')?.focus()");
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "Could not auto-focus input field");
                }
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        protected override Task<SignInFormViewModel> CreateSelectedItem()
        {
            return Task.FromResult(new SignInFormViewModel
            {
                PassKey = string.Empty,
                NickName = NickName,
                DeviceString = Guid.NewGuid().ToString()
            });
        }

        /// <summary>
        /// Handles the auth code submission with dual-purpose logic
        /// </summary>
        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            {
                var token = await StorageService.GetValue("auth_token");
                if (authResult == "Authenticate Online" || string.IsNullOrEmpty(token))
                {
                    var scope = ScopeFactory.CreateScope();
                    var signInClientService = scope.ServiceProvider.GetRequiredKeyedService<ISignInFormDataService>("client");
                    var selectedBusinessObject = ConvertViewModelToBusinessModel(SelectedItem);
                    if (!string.IsNullOrEmpty(selectedBusinessObject.NickName))
                        selectedBusinessObject.PassKey = $"{selectedBusinessObject.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";

                    authResult = await signInClientService.SaveAsync(selectedBusinessObject);
                    if (authResult == "Register")
                    {
                        await StorageService.SetValue(SelectedItem.PassKey!, "code");
                        Navigation.NavigateTo($"/register?code={SelectedItem.PassKey}", replace: true);
                        await CloseDialog();
                        return;
                    }
                    else if (authResult == "Login" || (authResult.Contains('{') || string.IsNullOrEmpty(token)))
                    {
                        await StorageService.SetValue(SelectedItem.PassKey!, ClaimTypes.Name);
                        Navigation.NavigateTo($"/login?user={SelectedItem.PassKey}", replace: true);
                        await CloseDialog();
                        return;
                    }
                }
                //var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);
                //await StorageService.SetValue(authClaims.Token, "auth_token");
                //await StorageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                //await StorageService.SetValue(authClaims.Username, ClaimTypes.Name);
                // Navigate directly to chat threads
                Navigation.NavigateTo("/chat", replace: true);
                await CloseDialog();

            }

            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing authentication result");
                Error = "Invalid Code.";
            }
        }

        //protected override SigninFormBusinessObject ConvertViewModelToBusinessModel(SignInFormViewModel formViewModel)
        //{
        //    var passKey = formViewModel.PassKey?.Trim();
        //    if (!string.IsNullOrEmpty(formViewModel.NickName))
        //    { 
        //        passKey = $"{formViewModel.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";
        //    };

        //    return new SigninFormBusinessObject()
        //    {
        //        NickName = formViewModel.NickName.Trim(),
        //        DeviceString = formViewModel.DeviceString,
        //        PassKey = passKey
        //    };
        //}

        /// <summary>
        /// Obfuscates the username to show only last 2-3 characters
        /// </summary>
        private string ObfuscateUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
                return string.Empty;

            if (username.Length <= 2)
                return new string('*', username.Length);

            // Show last 2 characters for usernames 3-5 chars, last 3 for longer
            int visibleChars = username.Length <= 5 ? 2 : 3;
            int hiddenChars = username.Length - visibleChars;

            return new string('*', hiddenChars) + username.Substring(hiddenChars);
        }

        /// <summary>
        /// Resets registration by clearing stored username and related data
        /// </summary>
        private async Task ResetRegistration()
        {
            try
            {
                // Show loading state during reset
                IsWorking = true;
                StateHasChanged();

                // Clear stored username and related authentication data
                await StorageService.RemoveValue(ClaimTypes.Name);
                await StorageService.RemoveValue(ClaimTypes.NameIdentifier);
                await StorageService.RemoveValue("pub2e_");
                await StorageService.RemoveValue("pub1o_");
                await StorageService.RemoveValue("auth_token");
                await StorageService.RemoveValue("refresh_token");
                await StorageService.RemoveValue("code");

                // Reset component state
                NickName = null;
                IsWorking = false;

                await InvokeAsync(() => StateHasChanged());
                await CloseDialog();
                ShowDialog<FakeCaptchaScreen>("Activate your News App", null, Size.Md, Position_.Center, true, P(true, "KeepAlive"));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error resetting registration");
                Error = "Error resetting registration. Please try again.";
                IsWorking = false;
                StateHasChanged();
            }
        }

        ///// <summary>
        ///// Custom error handling for authentication failures
        ///// </summary>
        //protected override void LogAndDisplayError(Exception ex)
        //{
        //    Logger.LogError(ex, "Captcha verification error");

        //    Error = ex switch
        //    {
        //        UnauthorizedAccessException => "Invalid verification code. Please check your code and try again.",
        //        ArgumentException => "Please enter a valid verification code.",
        //        InvalidOperationException => "Unable to verify at this time. Please try again later.",
        //        TimeoutException => "Verification timed out. Please try again.",
        //        _ => "Verification failed. Please check your code and try again."
        //    };
        //}


    }

}
