﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.WebApis.Controller.Profile;
[ApiController, Route("{culture:culture}/api/[controller]/[action]")]
public class AvatarSelectionListingController : ControllerBase, IAvatarSelectionListingDataService
{

	private readonly IAvatarSelectionListingDataService dataService;

	public AvatarSelectionListingController(IAvatarSelectionListingDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<PagedDataList<AvatarSelectionListingBusinessObject>> GetPaginatedItems([FromQuery] AvatarSelectionFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
