﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Profile;
using Platform.Client.Services.Features.Profile;
namespace Platform.Client.Common.Features.Profile;
public class AvatarSelectionListingViewBase : ListingBaseMaui<AvatarSelectionListingViewModel,AvatarSelectionListingBusinessObject,AvatarSelectionFilterViewModel,AvatarSelectionFilterBusinessObject, IAvatarSelectionListingDataService>
{
	public AvatarSelectionListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
	{
	}
}


public partial class AvatarSelectionListingView : AvatarSelectionListingViewBase
{
	public AvatarSelectionListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
	{
	InitializeComponent();
	}
}


