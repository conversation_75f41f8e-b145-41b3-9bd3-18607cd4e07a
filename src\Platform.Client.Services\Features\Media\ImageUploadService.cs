using Microsoft.AspNetCore.Components.Forms;
using System.Net.Http.Json;

namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Implementation of image upload service with compression and validation
/// </summary>
public class ImageUploadService : IImageUploadService
{
    private readonly HttpClient _httpClient;
    private readonly IImageCompressionService _compressionService;

    public ImageUploadService(HttpClient httpClient, IImageCompressionService compressionService)
    {
        _httpClient = httpClient;
        _compressionService = compressionService;
    }

    public async Task<ImageUploadResult> UploadProfilePictureAsync(IBrowserFile file, int maxSizeKB = 2048)
    {
        try
        {
            // Validate the image
            var validation = ValidateImage(file, maxSizeKB);
            if (!validation.IsValid)
            {
                return new ImageUploadResult
                {
                    Success = false,
                    ErrorMessage = validation.ErrorMessage
                };
            }

            // Read and process the image
            var maxFileSize = maxSizeKB * 1024;
            using var stream = file.OpenReadStream(maxFileSize);
            var imageData = new byte[stream.Length];
            await stream.ReadAsync(imageData, 0, (int)stream.Length);

            // Compress and resize the image
            var processedImage = await ProcessImageAsync(imageData);
            var thumbnail = await GenerateThumbnailAsync(processedImage);

            // Create multipart form data
            using var content = new MultipartFormDataContent();
            content.Add(new ByteArrayContent(processedImage), "file", file.Name);
            content.Add(new ByteArrayContent(thumbnail), "thumbnail", $"thumb_{file.Name}");
            content.Add(new StringContent("profile"), "type");

            // Upload to server
            var response = await _httpClient.PostAsync("/api/media/upload", content);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<MediaUploadResponse>();
                return new ImageUploadResult
                {
                    Success = true,
                    ImageUrl = result?.FileUrl,
                    ThumbnailUrl = result?.ThumbnailUrl,
                    FileSizeBytes = processedImage.Length,
                    FileName = file.Name,
                    MimeType = file.ContentType
                };
            }
            else
            {
                return new ImageUploadResult
                {
                    Success = false,
                    ErrorMessage = "Failed to upload image to server"
                };
            }
        }
        catch (Exception ex)
        {
            return new ImageUploadResult
            {
                Success = false,
                ErrorMessage = $"Upload error: {ex.Message}"
            };
        }
    }

    public ImageValidationResult ValidateImage(IBrowserFile file, int maxSizeKB = 2048)
    {
        var result = new ImageValidationResult { IsValid = true };

        // Check file size
        var maxSizeBytes = maxSizeKB * 1024;
        if (file.Size > maxSizeBytes)
        {
            result.IsValid = false;
            result.Errors.Add($"File size ({file.Size / 1024:F1} KB) exceeds maximum allowed size ({maxSizeKB} KB)");
        }

        // Check file format
        if (!SupportedImageFormats.IsSupported(file.ContentType))
        {
            result.IsValid = false;
            result.Errors.Add($"Unsupported file format: {file.ContentType}. Supported formats: JPG, PNG, WebP");
        }

        // Check file extension
        if (!SupportedImageFormats.IsSupportedExtension(file.Name))
        {
            result.IsValid = false;
            result.Errors.Add($"Unsupported file extension. Supported extensions: {string.Join(", ", SupportedImageFormats.Extensions)}");
        }

        // Set error message
        if (!result.IsValid)
        {
            result.ErrorMessage = string.Join("; ", result.Errors);
        }

        return result;
    }

    public async Task<byte[]> ProcessImageAsync(byte[] imageData, int maxWidth = 400, int maxHeight = 400, int quality = 85)
    {
        return await _compressionService.CompressImageAsync(imageData, maxWidth, maxHeight, quality);
    }

    public async Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int size = 150)
    {
        return await _compressionService.CompressImageAsync(imageData, size, size, 80);
    }
}

/// <summary>
/// Response from media upload API
/// </summary>
public class MediaUploadResponse
{
    public string? FileId { get; set; }
    public string? FileName { get; set; }
    public long FileSizeBytes { get; set; }
    public string? MimeType { get; set; }
    public string? FileUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string? ThumbnailBase64 { get; set; }
}
