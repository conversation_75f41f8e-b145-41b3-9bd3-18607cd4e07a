using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Local file system implementation of media caching service
/// </summary>
public class MediaCacheService : IMediaCacheService
{
    private readonly ILogger<MediaCacheService> _logger;
    private readonly string _cacheDirectory;
    private readonly string _thumbnailDirectory;
    private readonly string _metadataFile;

    public MediaCacheService(ILogger<MediaCacheService> logger)
    {
        _logger = logger;

        // Use platform-agnostic cache directory
        var baseCacheDir = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
        _cacheDirectory = Path.Combine(baseCacheDir, "DeepMessage", "media");
        _thumbnailDirectory = Path.Combine(_cacheDirectory, "thumbnails");
        _metadataFile = Path.Combine(_cacheDirectory, "cache_metadata.json");

        // Ensure directories exist
        Directory.CreateDirectory(_cacheDirectory);
        Directory.CreateDirectory(_thumbnailDirectory);
    }

    public async Task<byte[]?> GetCachedImageAsync(string attachmentId)
    {
        try
        {
            var filePath = GetImageCachePath(attachmentId);
            if (File.Exists(filePath))
            {
                var data = await File.ReadAllBytesAsync(filePath);
                _logger.LogDebug("Retrieved cached image for attachment {AttachmentId}, size: {Size} bytes", 
                    attachmentId, data.Length);
                return data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached image for attachment {AttachmentId}", attachmentId);
            return null;
        }
    }

    public async Task<bool> CacheImageAsync(string attachmentId, byte[] imageData, string? mimeType = null)
    {
        try
        {
            var filePath = GetImageCachePath(attachmentId);
            await File.WriteAllBytesAsync(filePath, imageData);
            
            // Update metadata
            await UpdateCacheMetadataAsync(attachmentId, imageData.Length, mimeType, "image");
            
            _logger.LogDebug("Cached image for attachment {AttachmentId}, size: {Size} bytes", 
                attachmentId, imageData.Length);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching image for attachment {AttachmentId}", attachmentId);
            return false;
        }
    }

    public async Task<byte[]?> GetCachedThumbnailAsync(string attachmentId)
    {
        try
        {
            var filePath = GetThumbnailCachePath(attachmentId);
            if (File.Exists(filePath))
            {
                var data = await File.ReadAllBytesAsync(filePath);
                _logger.LogDebug("Retrieved cached thumbnail for attachment {AttachmentId}, size: {Size} bytes", 
                    attachmentId, data.Length);
                return data;
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached thumbnail for attachment {AttachmentId}", attachmentId);
            return null;
        }
    }

    public async Task<bool> CacheThumbnailAsync(string attachmentId, byte[] thumbnailData)
    {
        try
        {
            var filePath = GetThumbnailCachePath(attachmentId);
            await File.WriteAllBytesAsync(filePath, thumbnailData);
            
            // Update metadata
            await UpdateCacheMetadataAsync(attachmentId, thumbnailData.Length, "image/jpeg", "thumbnail");
            
            _logger.LogDebug("Cached thumbnail for attachment {AttachmentId}, size: {Size} bytes", 
                attachmentId, thumbnailData.Length);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching thumbnail for attachment {AttachmentId}", attachmentId);
            return false;
        }
    }

    public async Task<bool> RemoveCachedMediaAsync(string attachmentId)
    {
        try
        {
            var removed = false;
            
            // Remove image
            var imagePath = GetImageCachePath(attachmentId);
            if (File.Exists(imagePath))
            {
                File.Delete(imagePath);
                removed = true;
            }
            
            // Remove thumbnail
            var thumbnailPath = GetThumbnailCachePath(attachmentId);
            if (File.Exists(thumbnailPath))
            {
                File.Delete(thumbnailPath);
                removed = true;
            }
            
            // Update metadata
            if (removed)
            {
                await RemoveFromCacheMetadataAsync(attachmentId);
                _logger.LogDebug("Removed cached media for attachment {AttachmentId}", attachmentId);
            }
            
            return removed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cached media for attachment {AttachmentId}", attachmentId);
            return false;
        }
    }

    public async Task<long> GetCacheSizeAsync()
    {
        try
        {
            var totalSize = 0L;
            
            // Calculate size of all files in cache directory
            if (Directory.Exists(_cacheDirectory))
            {
                var files = Directory.GetFiles(_cacheDirectory, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;
                }
            }
            
            return totalSize;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating cache size");
            return 0;
        }
    }

    public async Task<int> ClearCacheAsync()
    {
        try
        {
            var filesRemoved = 0;
            
            if (Directory.Exists(_cacheDirectory))
            {
                var files = Directory.GetFiles(_cacheDirectory, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    try
                    {
                        File.Delete(file);
                        filesRemoved++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete cache file: {FilePath}", file);
                    }
                }
                
                // Clear metadata
                if (File.Exists(_metadataFile))
                {
                    File.Delete(_metadataFile);
                }
            }
            
            _logger.LogInformation("Cleared media cache, removed {FileCount} files", filesRemoved);
            return filesRemoved;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return 0;
        }
    }

    public async Task<int> CleanupCacheAsync(long maxCacheSizeBytes = 100_000_000, int maxAgeHours = 168)
    {
        try
        {
            var filesRemoved = 0;
            var cutoffDate = DateTime.Now.AddHours(-maxAgeHours);
            
            if (!Directory.Exists(_cacheDirectory))
                return 0;
            
            var files = Directory.GetFiles(_cacheDirectory, "*", SearchOption.AllDirectories)
                .Select(f => new FileInfo(f))
                .Where(f => f.Name != "cache_metadata.json")
                .ToList();
            
            // Remove files older than cutoff date
            var oldFiles = files.Where(f => f.LastWriteTime < cutoffDate).ToList();
            foreach (var file in oldFiles)
            {
                try
                {
                    File.Delete(file.FullName);
                    filesRemoved++;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old cache file: {FilePath}", file.FullName);
                }
            }
            
            // Check if cache size is still too large
            var currentSize = files.Where(f => f.Exists).Sum(f => f.Length);
            if (currentSize > maxCacheSizeBytes)
            {
                // Remove oldest files until under size limit
                var remainingFiles = files.Where(f => f.Exists && f.LastWriteTime >= cutoffDate)
                    .OrderBy(f => f.LastWriteTime)
                    .ToList();
                
                foreach (var file in remainingFiles)
                {
                    if (currentSize <= maxCacheSizeBytes)
                        break;
                        
                    try
                    {
                        currentSize -= file.Length;
                        File.Delete(file.FullName);
                        filesRemoved++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete cache file for size limit: {FilePath}", file.FullName);
                    }
                }
            }
            
            if (filesRemoved > 0)
            {
                _logger.LogInformation("Cache cleanup removed {FileCount} files", filesRemoved);
            }
            
            return filesRemoved;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during cache cleanup");
            return 0;
        }
    }

    public async Task<bool> IsCachedAsync(string attachmentId)
    {
        var imagePath = GetImageCachePath(attachmentId);
        var thumbnailPath = GetThumbnailCachePath(attachmentId);
        return File.Exists(imagePath) || File.Exists(thumbnailPath);
    }

    public async Task<MediaCacheStats> GetCacheStatsAsync()
    {
        try
        {
            var stats = new MediaCacheStats();
            
            if (!Directory.Exists(_cacheDirectory))
                return stats;
            
            var files = Directory.GetFiles(_cacheDirectory, "*", SearchOption.AllDirectories)
                .Select(f => new FileInfo(f))
                .Where(f => f.Name != "cache_metadata.json")
                .ToList();
            
            stats.TotalFiles = files.Count;
            stats.TotalSizeBytes = files.Sum(f => f.Length);
            stats.ImageFiles = files.Count(f => !f.FullName.Contains("thumbnails"));
            stats.ThumbnailFiles = files.Count(f => f.FullName.Contains("thumbnails"));
            
            if (files.Any())
            {
                stats.OldestFileDate = files.Min(f => f.LastWriteTime);
                stats.NewestFileDate = files.Max(f => f.LastWriteTime);
            }
            
            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache stats");
            return new MediaCacheStats();
        }
    }

    private string GetImageCachePath(string attachmentId)
    {
        return Path.Combine(_cacheDirectory, $"{SanitizeFileName(attachmentId)}.jpg");
    }

    private string GetThumbnailCachePath(string attachmentId)
    {
        return Path.Combine(_thumbnailDirectory, $"{SanitizeFileName(attachmentId)}_thumb.jpg");
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
    }

    private async Task UpdateCacheMetadataAsync(string attachmentId, long size, string? mimeType, string type)
    {
        // This could be expanded to store more detailed metadata if needed
        // For now, we rely on file system timestamps and sizes
        await Task.CompletedTask;
    }

    private async Task RemoveFromCacheMetadataAsync(string attachmentId)
    {
        // This could be expanded to remove metadata entries if needed
        await Task.CompletedTask;
    }
}
