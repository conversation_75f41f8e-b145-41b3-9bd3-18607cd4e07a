using Microsoft.AspNetCore.Components;
using DeepMessage.ServiceContracts.Features.Profile;
using Platform.Client.Services.Features.Profile;
using System.Text.Json;
using Microsoft.Extensions.Logging;

namespace Platform.Razor.Features.Profile.Form
{
    public partial class AvatarSelection
    {
        [Parameter] public string? Context { get; set; }
        [Parameter] public string? TargetId { get; set; }

   

        protected override async Task OnInitializedAsync()
        {
            // Set context and target from parameters if provided
            if (!string.IsNullOrEmpty(Context))
            {
                SelectedItem.Context = Context;
            }
            
            if (!string.IsNullOrEmpty(TargetId))
            {
                SelectedItem.TargetId = TargetId;
            }

            await base.OnInitializedAsync();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await LoadAvailableImages();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task LoadAvailableImages()
        {
            try
            {
                SelectedItem.IsLoadingImages = true;
                StateHasChanged();

                var businessModel = await DataService.GetItemByIdAsync(SelectedItem.TargetId ?? string.Empty);
                if (businessModel?.AvailableImages != null)
                {
                    SelectedItem.AvailableImages = businessModel.AvailableImages;
                }
            }
            catch (Exception ex)
            {
                Error = $"Failed to load avatars: {ex.Message}";
                Logger.LogError(ex, "Error loading available images");
            }
            finally
            {
                SelectedItem.IsLoadingImages = false;
                StateHasChanged();
            }
        }

        private void SelectImage(string imageId)
        {
            SelectedItem.SelectedImageId = imageId;
            SelectedItem.SuccessMessage = null;
            Error = null;
            StateHasChanged();
        }

        private string GetImageButtonClasses(string imageId)
        {
            var baseClasses = "relative block w-20 h-20 rounded-lg border-2 transition-all duration-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2";
            
            if (SelectedItem?.SelectedImageId == imageId)
            {
                return $"{baseClasses} border-gray-900 shadow-lg";
            }
            else
            {
                return $"{baseClasses} border-gray-200 hover:border-gray-400";
            }
        }

        private string GetImageDataUrl(string base64Content)
        {
            if (string.IsNullOrEmpty(base64Content))
                return string.Empty;

            // Check if it already has the data URL prefix
            if (base64Content.StartsWith("data:"))
                return base64Content;

            return $"data:image/png;base64,{base64Content}";
        }

        public override async Task OnAfterSaveAsync(string key)
        {
            try
            {
                // Parse the response to show success message
                var response = JsonSerializer.Deserialize<JsonElement>(key);
                if (response.GetProperty("success").GetBoolean())
                {
                    var message = response.GetProperty("message").GetString();
                    SelectedItem.SuccessMessage = message;
                    
                    // Publish event for profile refresh
                    PubSub.Hub.Default.Publish(new Tuple<string, string, dynamic>(OperationId, "Avatar Selected", key));
                    
                    // Auto-close after brief success display
                    await Task.Delay(1500);
                    await CloseDialog();
                }
                else
                {
                    Error = "Failed to save avatar selection";
                }
            }
            catch (Exception ex)
            {
                Error = $"Error processing save response: {ex.Message}";
                Logger.LogError(ex, "Error processing avatar selection save response");
            }
            
            await base.OnAfterSaveAsync(key);
        }
    }
}
