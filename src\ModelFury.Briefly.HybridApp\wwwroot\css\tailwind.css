﻿@tailwind base;
@tailwind components;
@tailwind utilities;

/* Briefly AI News - Nothing Phone Aesthetic Color System */
:root {
    --rz-input-height: 2.625rem;

    /* === SEMANTIC COLOR SYSTEM === */
    /* Primary Colors - Nothing Phone Black */
    --dm-primary-50: #fafafa;
    --dm-primary-100: #f5f5f5;
    --dm-primary-200: #eeeeee;
    --dm-primary-300: #e0e0e0;
    --dm-primary-400: #bdbdbd;
    --dm-primary-500: #9e9e9e;
    --dm-primary-600: #757575;
    --dm-primary-700: #424242;
    --dm-primary-800: #2c2c2c;
    --dm-primary-900: #1a1a1a;
    --dm-primary-950: #0d0d0d;

    /* Secondary Colors - Nothing Phone Red Accent */
    --dm-secondary-50: #fef2f2;
    --dm-secondary-100: #fee2e2;
    --dm-secondary-200: #fecaca;
    --dm-secondary-300: #fca5a5;
    --dm-secondary-400: #f87171;
    --dm-secondary-500: #ef4444;
    --dm-secondary-600: #dc2626;
    --dm-secondary-700: #b91c1c;
    --dm-secondary-800: #991b1b;
    --dm-secondary-900: #7f1d1d;
    --dm-secondary-950: #450a0a;

    /* === SEMANTIC TEXT COLORS === */
    /* WCAG AA Compliant - 4.5:1 contrast ratio minimum */
    --dm-text-primary: var(--dm-primary-900);      /* #1a1a1a on white = 12.6:1 ratio ✓ */
    --dm-text-secondary: var(--dm-primary-700);    /* #424242 on white = 7.0:1 ratio ✓ */
    --dm-text-tertiary: var(--dm-primary-600);     /* #757575 on white = 4.5:1 ratio ✓ */
    --dm-text-inverse: var(--dm-primary-50);       /* #fafafa on dark = 18.7:1 ratio ✓ */
    --dm-text-accent: var(--dm-secondary-600);     /* #dc2626 on white = 5.7:1 ratio ✓ */

    /* === SEMANTIC BACKGROUND COLORS === */
    --dm-bg-primary: var(--dm-primary-50);
    --dm-bg-secondary: var(--dm-primary-100);
    --dm-bg-tertiary: var(--dm-primary-200);
    --dm-bg-surface: #ffffff;

    /* === SEMANTIC BORDER COLORS === */
    --dm-border-primary: var(--dm-primary-200);
    --dm-border-secondary: var(--dm-primary-300);
    --dm-border-focus: var(--dm-primary-600);

    /* === COMPONENT-SPECIFIC COLORS === */
    --dm-header-bg: #ffffff;
    --dm-nav-bg: #ffffff;
    --dm-chat-bg: var(--dm-primary-50);
    --dm-dialog-bg: #ffffff;

    /* === CHAT BUBBLE COLORS === */
    --dm-chat-bubble-sent: var(--dm-primary-300);
    --dm-chat-bubble-received: var(--dm-primary-100);
    --dm-chat-input-bg: var(--dm-primary-200);
}

/* Dark Mode Support */
.dark {
    /* Primary Colors - Inverted for Dark Mode */
    --dm-text-primary: var(--dm-primary-50);
    --dm-text-secondary: var(--dm-primary-400);
    --dm-text-tertiary: var(--dm-primary-500);
    --dm-text-inverse: var(--dm-primary-900);
    --dm-text-accent: var(--dm-secondary-400);

    /* Background Colors - Dark Mode */
    --dm-bg-primary: var(--dm-primary-900);
    --dm-bg-secondary: var(--dm-primary-800);
    --dm-bg-tertiary: var(--dm-primary-700);
    --dm-bg-surface: var(--dm-primary-950);

    /* Border Colors - Dark Mode */
    --dm-border-primary: var(--dm-primary-700);
    --dm-border-secondary: var(--dm-primary-600);
    --dm-border-focus: var(--dm-primary-400);

    /* Component Colors - Dark Mode */
    --dm-header-bg: var(--dm-primary-950);
    --dm-nav-bg: var(--dm-primary-950);
    --dm-chat-bg: var(--dm-primary-900);
    --dm-dialog-bg: var(--dm-primary-950);

    /* Chat Bubble Colors - Dark Mode */
    --dm-chat-bubble-sent: var(--dm-primary-700);
    --dm-chat-bubble-received: var(--dm-primary-800);
    --dm-chat-input-bg: var(--dm-primary-700);
}

/* === COMPONENT STYLES === */
@layer components {
    /* Enhanced Button Styles - WCAG AA Compliant */
    .btn-primary {
        @apply bg-primary-800 text-white px-4 py-2 rounded-lg hover:bg-primary-900 transition-theme focus-ring-primary touch-target;
    }

    .btn-secondary {
        @apply bg-secondary-500 text-white px-4 py-2 rounded-lg hover:bg-secondary-600 transition-theme focus-ring-secondary touch-target;
    }

    .btn-outline {
        @apply border border-border bg-transparent text-primary px-4 py-2 rounded-lg hover:bg-muted transition-theme focus-ring-primary touch-target;
    }

    .btn-ghost {
        @apply bg-transparent text-primary px-4 py-2 rounded-lg hover:bg-muted transition-theme focus-ring-primary touch-target;
    }

    .btn-link {
        @apply bg-transparent text-secondary px-2 py-1 rounded hover:text-primary hover:underline transition-theme focus-ring-primary touch-target-sm;
    }

    /* Button Sizes */
    .btn-xs {
        @apply px-2 py-1 text-xs rounded-md;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-sm rounded-md;
    }

    .btn-lg {
        @apply px-6 py-3 text-lg rounded-xl;
    }

    .btn-xl {
        @apply px-8 py-4 text-xl rounded-2xl;
    }

    /* Button States */
    .btn:disabled {
        @apply opacity-50 cursor-not-allowed;
    }

    .btn-loading {
        @apply relative overflow-hidden;
    }

    .btn-loading::before {
        content: '';
        @apply absolute inset-0 bg-white/20 animate-pulse;
    }

    /* Enhanced Form Styles */
    .form-control {
        @apply w-full px-3 py-2 border border-border rounded-lg bg-surface text-primary placeholder:text-secondary focus-theme transition-theme;
    }

    .form-control:invalid {
        @apply border-secondary-300 focus:border-secondary-500 focus:ring-secondary-200;
    }

    .form-control:valid {
        @apply border-green-300 focus:border-green-500 focus:ring-green-200;
    }

    .form-select {
        @apply w-full px-3 py-2 border border-border rounded-lg bg-surface text-primary focus-theme transition-theme;
    }

    .form-textarea {
        @apply w-full px-3 py-2 border border-border rounded-lg bg-surface text-primary placeholder:text-secondary focus-theme transition-theme resize-y min-h-[100px];
    }

    .form-checkbox {
        @apply h-4 w-4 text-primary-600 border-border rounded focus:ring-primary-500 focus:ring-2;
    }

    .form-radio {
        @apply h-4 w-4 text-primary-600 border-border focus:ring-primary-500 focus:ring-2;
    }

    .form-label {
        @apply block text-sm font-medium text-primary mb-2;
    }

    .form-help {
        @apply text-xs text-secondary mt-1;
    }

    .form-error {
        @apply text-xs text-secondary-600 mt-1;
    }

    /* Form Group Styles */
    .form-group {
        @apply space-y-2;
    }

    .form-group-inline {
        @apply flex items-center space-x-3;
    }

    /* === AUTHENTICATION FORM ENHANCEMENTS === */

    /* Mobile Container for Auth Forms */
    .mobile-container {
        @apply max-w-md mx-auto px-4 sm:px-6;
    }

    /* Touch Spacing for Mobile-First Design */
    .touch-spacing {
        @apply p-4 sm:p-6 lg:p-8;
    }

    /* Responsive Text Sizes */
    .text-responsive-xl {
        @apply text-2xl sm:text-3xl;
    }

    .text-responsive-sm {
        @apply text-sm sm:text-base;
    }

    /* Enhanced Touch Targets */
    .touch-target-sm {
        @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
    }

    /* Password Toggle Button */
    .password-toggle {
        @apply absolute inset-y-0 right-0 pr-3 flex items-center text-primary-500 hover:text-primary transition-theme touch-target-sm focus-ring-primary;
    }

    /* Form Container with Nothing Phone Aesthetic */
    .auth-form-container {
        @apply bg-surface shadow-theme-xl rounded-lg touch-spacing space-y-6 border border-border;
    }

    /* Auth Header Icon */
    .auth-header-icon {
        @apply mx-auto h-16 w-16 bg-primary-800 rounded-full flex items-center justify-center mb-6 shadow-theme-lg;
    }

    /* Auth Form Button */
    .auth-form-button {
        @apply btn-primary relative w-full flex justify-center py-3 px-4 disabled:opacity-50 disabled:cursor-not-allowed shadow-theme-lg hover:shadow-theme-xl;
    }

    /* Loading Spinner for Auth Forms */
    .auth-loading-spinner {
        @apply animate-spin rounded-full h-5 w-5 border-2 border-white/30 border-t-white -ml-1 mr-3;
    }

    /* Auth Form Error Display */
    .auth-error-container {
        @apply bg-secondary-50 border border-secondary-200 rounded-lg p-4;
    }

    /* Auth Form Link */
    .auth-form-link {
        @apply font-medium text-primary-600 hover:text-primary-700 transition-theme focus-ring-primary;
    }

    /* Auth Form Divider */
    .auth-form-divider {
        @apply border-t border-border pt-6;
    }

    /* Chat Interface Styles */
    .chat-card {
        @apply flex w-full items-center gap-3 px-5 py-2 hover:bg-muted transition-theme cursor-pointer;
    }

    .chat-container {
        @apply bg-chat min-h-screen;
    }

    /* SVG Chat Backgrounds - Nothing Phone Aesthetic */
    .chat-bg-abstract {
        background-image: url('../images/chat-bg-abstract.svg');
        background-size: 400px 600px;
        background-repeat: repeat;
        background-position: center;
        @apply bg-chat;
    }

    .chat-bg-dynamic {
        background-image: url('../images/chat-bg-dynamic.svg');
        background-size: 400px 600px;
        background-repeat: repeat;
        background-position: center;
        @apply bg-chat;
    }

    .chat-bg-minimal {
        background-image: url('../images/chat-bg-minimal.svg');
        background-size: 400px 600px;
        background-repeat: repeat;
        background-position: center;
        @apply bg-chat;
    }

    .chat-bg-animated {
        background-image: url('../images/chat-bg-animated.svg');
        background-size: 400px 600px;
        background-repeat: repeat;
        background-position: center;
        @apply bg-chat;
    }

    .chat-bg-sleek {
        background-image: url('../images/chat-bg-sleek.svg');
        background-size: 400px 600px;
        background-repeat: repeat;
        background-position: center;
        @apply bg-white;
    }

    /* Responsive Chat Backgrounds */
    @media (max-width: 640px) {
        .chat-bg-abstract,
        .chat-bg-dynamic,
        .chat-bg-minimal,
        .chat-bg-animated,
        .chat-bg-sleek {
            background-size: 300px 450px;
        }

        /* Mobile message bubbles */
        .message-bubble-sent,
        .message-bubble-received {
            max-width: 280px;
            font-size: 14px;
        }
    }

    @media (min-width: 1024px) {
        .chat-bg-abstract,
        .chat-bg-dynamic,
        .chat-bg-minimal,
        .chat-bg-animated,
        .chat-bg-sleek {
            background-size: 500px 750px;
        }

        /* Desktop message bubbles */
        .message-bubble-sent,
        .message-bubble-received {
            max-width: 400px;
            font-size: 15px;
        }
    }

    /* Chat Background Variants for Different Contexts */
    .chat-bg-conversation {
        @apply chat-bg-dynamic;
    }

    .chat-bg-group {
        @apply chat-bg-abstract;
    }

    .chat-bg-private {
        @apply chat-bg-minimal;
    }

    .chat-bg-premium {
        @apply chat-bg-animated;
    }

    /* Motion Preference Support */
    @media (prefers-reduced-motion: reduce) {
        .chat-bg-animated {
            background-image: url('../images/chat-bg-dynamic.svg');
        }
    }

    /* Dark Mode Chat Backgrounds */
    .dark .chat-bg-abstract,
    .dark .chat-bg-dynamic,
    .dark .chat-bg-minimal {
        filter: invert(1) hue-rotate(180deg) brightness(0.9) contrast(1.1);
        opacity: 0.8;
    }

    .chat-bubble-sent {
        @apply bg-bubble-sent text-primary rounded-2xl px-4 py-2 max-w-xs ml-auto;
    }

    .chat-bubble-received {
        @apply bg-bubble-received text-primary rounded-2xl px-4 py-2 max-w-xs mr-auto;
    }

    .chat-input {
        @apply bg-input-bg border border-border rounded-3xl px-4 py-3 text-primary placeholder:text-secondary focus-theme transition-theme;
    }

    /* === SLEEK MINIMAL MESSAGE SYSTEM === */

    /* Message Bubbles - Sleek Minimal Design */
    .message-bubble-sent {
        @apply bg-gray-200 text-gray-900 rounded-2xl px-4 py-3 max-w-xs ml-auto shadow-sm;
        border-radius: 18px;
        font-size: 15px;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .message-bubble-received {
        @apply bg-gray-100 text-gray-900 rounded-2xl px-4 py-3 max-w-xs mr-auto shadow-sm;
        border-radius: 18px;
        font-size: 15px;
        line-height: 1.4;
        word-wrap: break-word;
    }

    /* Message Container */
    .message-container-sent {
        @apply flex flex-col items-end mb-4 px-4;
    }

    .message-container-received {
        @apply flex flex-col items-start mb-4 px-4;
    }

    /* Timestamp Styling */
    .message-timestamp {
        @apply text-xs text-gray-500 mt-1 px-2;
        font-size: 11px;
    }

    /* Read Receipt */
    .message-status {
        @apply inline-flex items-center ml-2;
    }

    .message-status-dot {
        @apply w-2 h-2 bg-red-500 rounded-full;
    }

    /* Chat Container - Sleek Minimal */
    .chat-container-sleek {
        @apply chat-bg-sleek;
        padding-top: 20px;
        padding-bottom: 100px;
    }

    /* === DATE SEPARATORS & SCROLL INDICATORS === */

    /* Date Separator */
    .date-separator {
        @apply flex items-center justify-center py-4 px-4;
    }

    .date-separator-line {
        @apply flex-1 h-px bg-gray-200;
    }

    .date-separator-text {
        @apply px-4 text-xs font-medium text-gray-500 bg-white;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }

    /* Scroll Date Indicator */
    .scroll-date-indicator {
        @apply fixed top-20 left-1/2 transform -translate-x-1/2 z-40;
        @apply bg-gray-900 text-white text-sm font-medium px-4 py-2 rounded-full shadow-lg;
        @apply transition-all duration-300 ease-in-out;
        backdrop-filter: blur(10px);
        background-color: rgba(17, 24, 39, 0.9);
    }

    .scroll-date-indicator.show {
        @apply opacity-100 translate-y-0;
    }

    .scroll-date-indicator.hide {
        @apply opacity-0 -translate-y-2;
    }

    /* Responsive adjustments */
    @media (max-width: 640px) {
        .scroll-date-indicator {
            @apply top-16 text-xs px-3 py-1.5;
        }

        .date-separator-text {
            @apply text-2xs px-3;
        }
    }

    /* === IMAGE UPLOAD COMPONENTS === */

    /* Image Upload Container */
    .image-upload-container {
        @apply space-y-3;
    }

    /* Upload Button Overlay */
    .upload-button-overlay {
        @apply absolute bottom-0 right-0 bg-primary-800 hover:bg-primary-900 text-white rounded-full shadow-lg transition-theme focus-ring-primary disabled:opacity-50;
    }

    /* Upload Progress Bar */
    .upload-progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
    }

    .upload-progress-fill {
        @apply bg-primary-600 h-full transition-all duration-300 ease-out;
    }

    /* Image Preview */
    .image-preview {
        @apply relative inline-block;
    }

    .image-preview img {
        @apply object-cover border-2 border-border shadow-theme-sm;
    }

    /* Profile Picture Sizes */
    .profile-picture-sm {
        @apply w-16 h-16 rounded-full;
    }

    .profile-picture-md {
        @apply w-24 h-24 rounded-full;
    }

    .profile-picture-lg {
        @apply w-32 h-32 rounded-full;
    }

    .profile-picture-xl {
        @apply w-40 h-40 rounded-full;
    }

    /* Upload States */
    .upload-state-idle {
        @apply opacity-100;
    }

    .upload-state-uploading {
        @apply opacity-75 pointer-events-none;
    }

    .upload-state-success {
        @apply opacity-100;
    }

    .upload-state-error {
        @apply opacity-100 border-secondary-300;
    }

    /* === NEWS LISTING WITH THUMBNAILS === */

    /* News Article Cards */
    .news-article-card {
        @apply bg-surface rounded-xl shadow-theme-sm hover:shadow-theme-md transition-shadow duration-300 border border-border overflow-hidden cursor-pointer;
    }

    .news-article-card:hover {
        @apply shadow-theme-lg;
    }

    /* News Thumbnail Container */
    .news-thumbnail-container {
        @apply relative overflow-hidden;
        height: 200px;
    }

     @media (min-width: 768px) {
        .news-thumbnail-container {
            height: 224px;
        }
    }

    /* News Thumbnail Image */
    .news-thumbnail-image {
        @apply w-full h-full object-cover transition-transform duration-300;
    }

    .news-article-card:hover .news-thumbnail-image {
        @apply scale-105;
    } 

    /* News Thumbnail Overlay */
    .news-thumbnail-overlay {
        @apply absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300;
    }

    .news-article-card:hover .news-thumbnail-overlay {
        @apply opacity-100;
    }

    /* News Thumbnail Fallback */
    .news-thumbnail-fallback {
        @apply absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center;
    }

    .dark .news-thumbnail-fallback {
        @apply from-gray-700 to-gray-800;
    }

    /* News External Link Indicator */
    .news-external-indicator {
        @apply absolute top-3 right-3 opacity-0 transition-opacity duration-200;
    }

    .news-article-card:hover .news-external-indicator {
        @apply opacity-100;
    }

    .news-external-indicator-bg {
        @apply bg-white/90 backdrop-blur-sm rounded-full p-2;
    }

    .dark .news-external-indicator-bg {
        @apply bg-gray-800/90;
    }

    /* News Content */
    .news-content {
        @apply p-6;
    }

    /* News Title */
    .news-title {
        @apply text-xl font-bold text-primary mb-3 transition-colors duration-200 line-clamp-2;
    }

    .news-article-card:hover .news-title {
        @apply text-secondary-600;
    }

    /* News Description */
    .news-description {
        @apply text-secondary leading-relaxed line-clamp-3 mb-4;
    }

    /* News Read More Link */
    .news-read-more {
        @apply flex items-center text-secondary-600 font-medium text-sm transition-colors duration-200;
    }

    .news-article-card:hover .news-read-more {
        @apply text-secondary-700;
    }

    .news-read-more-arrow {
        @apply ml-1 h-4 w-4 transition-transform duration-200;
    }

    .news-article-card:hover .news-read-more-arrow {
        @apply translate-x-1;
    }

    /* News Grid Responsive */
    .news-grid {
        @apply grid gap-6;
    }

    @media (min-width: 768px) {
        .news-grid {
            @apply gap-8;
        }
    }

    @media (min-width: 1024px) {
        .news-grid {
            @apply grid-cols-2;
        }
    }

    @media (min-width: 1280px) {
        .news-grid {
            @apply grid-cols-3;
        }
    }

    /* News Date Badge */
    .news-date {
        @apply text-sm text-secondary font-medium;
    }

    /* News Loading Skeleton */
    .news-skeleton {
        @apply animate-pulse;
    }

    .news-skeleton-image {
        @apply bg-gray-200 h-48 md:h-56 rounded-t-xl;
    }

    .dark .news-skeleton-image {
        @apply bg-gray-700;
    }

    .news-skeleton-content {
        @apply p-6 space-y-3;
    }

    .news-skeleton-title {
        @apply h-6 bg-gray-200 rounded;
    }

    .dark .news-skeleton-title {
        @apply bg-gray-700;
    }

    .news-skeleton-description {
        @apply space-y-2;
    }

    .news-skeleton-line {
        @apply h-4 bg-gray-200 rounded;
    }

    .dark .news-skeleton-line {
        @apply bg-gray-700;
    }

    /* Avatar Styles */
    .avatar {
        @apply relative inline-flex items-center justify-center rounded-full bg-muted border border-border;
    }

    .avatar-sm {
        @apply w-8 h-8 text-xs;
    }

    .avatar-md {
        @apply w-10 h-10 text-sm;
    }

    .avatar-lg {
        @apply w-12 h-12 text-base;
    }

    .avatar-xl {
        @apply w-16 h-16 text-lg;
    }

    .avatar-initials {
        @apply font-medium text-primary select-none;
    }

    .avatar-image {
        @apply w-full h-full object-cover rounded-full;
    }

    /* Navigation Styles */
    .nav-header {
        @apply bg-header border-b border-border px-4 py-3 shadow-theme-sm;
    }

    .nav-bottom {
        @apply bg-nav border-t border-border shadow-theme-sm;
    }

    .nav-tab {
        @apply flex flex-col items-center justify-center p-2 text-secondary hover:text-primary transition-theme;
    }

    .nav-tab.active {
        @apply text-primary bg-muted;
    }

    /* Dialog & Modal Styles */
    .dialog {
        @apply bg-dialog border border-border rounded-lg shadow-theme-lg;
    }

    .dialog-header {
        @apply px-6 py-4 border-b border-border;
    }

    .dialog-body {
        @apply px-6 py-4;
    }

    .dialog-footer {
        @apply px-6 py-4 border-t border-border bg-muted/30;
    }

    /* Loading States */
    .loading-spinner {
        @apply animate-spin rounded-full border-2 border-primary border-t-transparent;
    }

    .loading-spinner-sm {
        @apply h-4 w-4;
    }

    .loading-spinner-md {
        @apply h-6 w-6;
    }

    .loading-spinner-lg {
        @apply h-8 w-8;
    }

    /* Error States */
    .error-container {
        @apply bg-secondary-50 border border-secondary-200 rounded-lg p-4;
    }

    .error-icon {
        @apply h-5 w-5 text-secondary-600;
    }

    .error-title {
        @apply text-sm font-semibold text-secondary-800;
    }

    .error-message {
        @apply text-sm text-secondary-700;
    }

    /* Success States */
    .success-container {
        @apply bg-green-50 border border-green-200 rounded-lg p-4;
    }

    .success-icon {
        @apply h-5 w-5 text-green-600;
    }

    .success-title {
        @apply text-sm font-semibold text-green-800;
    }

    .success-message {
        @apply text-sm text-green-700;
    }
}

/* === UTILITY CLASSES === */
@layer utilities {
    /* Enhanced Theme Utilities */
    .transition-theme {
        @apply transition-all duration-200 ease-in-out;
    }

    .transition-theme-all {
        @apply transition-all duration-300 ease-in-out;
    }

    .focus-theme {
        @apply focus:outline-none focus:ring-2 focus:ring-border-focus focus:ring-offset-2 focus:ring-offset-surface;
    }

    .shadow-theme-sm {
        @apply shadow-sm;
    }

    .shadow-theme-md {
        @apply shadow-md;
    }

    .shadow-theme-lg {
        @apply shadow-lg;
    }

    .shadow-theme-xl {
        @apply shadow-xl;
    }

    .shadow-theme-2xl {
        @apply shadow-2xl;
    }

    /* Text Visibility Utilities */
    .text-visible {
        @apply text-primary;
    }

    .text-visible-secondary {
        @apply text-secondary;
    }

    .text-visible-muted {
        @apply text-tertiary;
    }

    /* Background Utilities for Better Contrast */
    .bg-subtle-contrast {
        @apply bg-muted;
    }

    .bg-chat-area {
        @apply bg-chat;
    }

    /* Border Utilities */
    .border-visible {
        @apply border border-border;
    }

    .border-visible-focus {
        @apply border border-border-focus;
    }

    /* Safe Area Utilities */
    .safe-area-top {
        padding-top: env(safe-area-inset-top);
    }

    .safe-area-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .safe-area-left {
        padding-left: env(safe-area-inset-left);
    }

    .safe-area-right {
        padding-right: env(safe-area-inset-right);
    }

    /* === ACCESSIBILITY UTILITIES === */
    /* Touch Target Utilities - Minimum 44px for mobile */
    .touch-target {
        @apply min-h-[44px] min-w-[44px];
    }

    .touch-target-sm {
        @apply min-h-[36px] min-w-[36px];
    }

    .touch-target-lg {
        @apply min-h-[52px] min-w-[52px];
    }

    /* Screen Reader Utilities */
    .sr-only {
        @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
        clip: rect(0, 0, 0, 0);
    }

    .sr-only-focusable:focus {
        @apply static w-auto h-auto p-0 m-0 overflow-visible whitespace-normal;
        clip: auto;
    }

    /* Focus Utilities for Keyboard Navigation */
    .focus-visible-only:focus:not(:focus-visible) {
        outline: none;
    }

    .focus-ring-primary:focus-visible {
        @apply outline-none ring-2 ring-primary-600 ring-offset-2 ring-offset-surface;
    }

    .focus-ring-secondary:focus-visible {
        @apply outline-none ring-2 ring-secondary-600 ring-offset-2 ring-offset-surface;
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
        .text-primary {
            color: #000000 !important;
        }

        .text-secondary {
            color: #333333 !important;
        }

        .border-border {
            border-color: #000000 !important;
        }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
        .transition-theme,
        .transition-theme-all,
        .loading-spinner {
            transition: none !important;
            animation: none !important;
        }
    }

    /* === MOBILE-FIRST RESPONSIVE DESIGN === */
    /* Mobile Optimizations */
    .mobile-container {
        @apply w-full max-w-sm mx-auto px-4;
    }

    .mobile-header {
        @apply sticky top-0 z-40 bg-header border-b border-border safe-area-top;
    }

    .mobile-content {
        @apply flex-1 overflow-y-auto safe-area-bottom pb-20;
    }

    .mobile-footer {
        @apply fixed bottom-0 left-0 right-0 z-50 safe-area-bottom;
    }

    /* Touch-Friendly Spacing */
    .touch-spacing {
        @apply p-4 space-y-4;
    }

    .touch-spacing-sm {
        @apply p-2 space-y-2;
    }

    .touch-spacing-lg {
        @apply p-6 space-y-6;
    }

    /* Responsive Text Sizes */
    .text-responsive-xs {
        @apply text-xs sm:text-sm;
    }

    .text-responsive-sm {
        @apply text-sm sm:text-base;
    }

    .text-responsive-base {
        @apply text-base sm:text-lg;
    }

    .text-responsive-lg {
        @apply text-lg sm:text-xl;
    }

    .text-responsive-xl {
        @apply text-xl sm:text-2xl;
    }

    /* Mobile Dialog Styles */
    .mobile-dialog {
        @apply fixed inset-x-4 bottom-4 top-auto sm:inset-auto sm:top-1/2 sm:left-1/2 sm:transform sm:-translate-x-1/2 sm:-translate-y-1/2 sm:max-w-md sm:w-full;
    }

    .mobile-dialog-fullscreen {
        @apply fixed inset-0 sm:inset-4 sm:rounded-lg;
    }

    /* Gesture Support */
    .swipe-indicator {
        @apply w-12 h-1 bg-primary-300 rounded-full mx-auto mb-4;
    }

    .pull-to-refresh {
        @apply transform transition-transform duration-200;
    }

    .pull-to-refresh.pulling {
        @apply translate-y-16;
    }
}
