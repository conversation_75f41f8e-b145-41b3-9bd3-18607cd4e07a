﻿using DeepMessage.Client.Common.Data;
using DeepMessage.Framework.Core;
using DeepMessage.MauiApp.Services;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.AuthCodes;
using DeepMessage.ServiceContracts.Features.Configurations;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using DeepMessage.ServiceContracts.Features.Home;
using DeepMessage.ServiceContracts.Features.Profile;
using KPlatform.Framework.Core;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Features.AuthCodes;
using Platform.Client.Services.Features.Configurations;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Features.Conversation.ChatThreads.Form;
using Platform.Client.Services.Features.Friends;
using Platform.Client.Services.Features.Profile;
using Platform.Client.Services;
using Platform.Client.Services.Features.Home;
using Platform.Framework.Core;
using Plugin.Firebase.CloudMessaging;
using Plugin.Firebase.Core.Platforms.Android;

namespace ModelFury.Briefly.HybridApp
{
    public static class MauiProgram
    {

#if DEBUG
        public static string APIUrl = "https://********:7073";
        //public static string APIUrl = "https://briefly.azurewebsites.net";
#else
        //public static string APIUrl = "https://********:7073";
        public static string APIUrl = "https://briefly.azurewebsites.net";
#endif

        public static string? ChatHubUrl;
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility", Justification = "<Pending>")]
        public static Microsoft.Maui.Hosting.MauiApp CreateMauiApp()
        {
            ChatHubUrl = $"{APIUrl}/chathub";
            var builder = Microsoft.Maui.Hosting.MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                    fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    fonts.AddFont("Jost-Regular.ttf", "Jost");
                });
            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.RegisterFirebaseServices();
            builder.Services.AddSingleton<ChatSyncUpService>();
            builder.Services.AddSingleton<SignalRClientService>();
            builder.Services.AddSingleton<DialogService>();
            builder.Services.AddSingleton<KtNotificationService>();
            builder.Services.AddSingleton<AlertService>();
            builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthStateProvider>();
            var dbPath = Path.Combine(FileSystem.AppDataDirectory, "deep_v5g.db");
            builder.Services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite($"Filename={dbPath}"));

            //#if DEBUG
            //builder.Logging
            //.ClearProviders()
            //.AddDebug() // Output to logcat
            //.AddConsole() // Output to console
            //.SetMinimumLevel(LogLevel.Debug) // Global minimum level
            //.AddFilter((category, level) =>
            //{
            //    // Suppress EF Core and noisy stuff
            //    if (category.StartsWith("Microsoft.EntityFrameworkCore"))
            //        return false;
            //    if (category.StartsWith("Microsoft"))
            //        return false;
            //    if (category.StartsWith("System"))
            //        return false;

            //    // Only allow your app's namespace logs
            //    return category.StartsWith("DeepMessage");
            //});
            ////#endif
            HttpClientHandler insecureHandler = GetInsecureHandler();

            builder.Services.AddHttpClient<BaseHttpClient, HttpTokenClient>("ServerAPI", client =>
            {

                client.BaseAddress = new Uri(APIUrl);
                client.Timeout = TimeSpan.FromSeconds(5);

            }).ConfigurePrimaryHttpMessageHandler(() => insecureHandler);
            ;
            builder.Services.AddScoped<ILocalStorageService, LocalStorage>();
            builder.Services.AddScoped<INewsListingDataService, NewsLocalListingDataService>();
            builder.Services.AddKeyedScoped<INewsListingDataService, NewsClientSideListingDataService>("client");
            // Register encryption and security services
            builder.Services.AddScoped<Platform.Client.Services.Services.IClientEncryptionService, Platform.Client.Services.Services.ClientEncryptionService>();
            builder.Services.AddSingleton<Platform.Client.Services.Services.ISecureKeyManager, Platform.Client.Services.Services.SecureKeyManager>();
            builder.Services.AddScoped<Platform.Client.Services.Services.IPasswordPromptService, Platform.Client.Services.Services.PasswordPromptService>();

            // Register media services
            builder.Services.AddScoped<Platform.Client.Services.Features.Media.IImageCompressionService, Platform.Client.Services.Features.Media.ImageCompressionService>();
            builder.Services.AddScoped<Platform.Client.Services.Features.Media.IImageUploadService, Platform.Client.Services.Features.Media.ImageUploadService>();
            builder.Services.AddSingleton<Platform.Client.Services.Features.Media.IMediaCacheService, Platform.Client.Services.Features.Media.MediaCacheService>();

            builder.Services.AddScoped<ISignupFormDataService, SignupClientSideFormDataService>();

            // Vertical slice pattern: Default (offline-first) and backup (online) services
            builder.Services.AddScoped<ISignInFormDataService, Platform.Client.Services.Features.Account.SigninForm.SignInOfflineFormDataService>();
            builder.Services.AddKeyedScoped<ISignInFormDataService, SignInClientSideFormDataService>("client");
            builder.Services.AddScoped<IProfileListingDataService, ProfileClientSideListingDataService>();
            builder.Services.AddScoped<IProfileFormDataService, ProfileClientSideFormDataService>();
            builder.Services.AddScoped<IFriendFormDataService, FriendClientSideFormDataService>();
            builder.Services.AddScoped<IAvatarFormDataService, AvatarClientSideFormDataService>();
            builder.Services.AddScoped<IAvatarSelectionFormDataService, AvatarSelectionClientSideFormDataService>();
            builder.Services.AddKeyedScoped<IFriendsListingDataService, FriendsClientSideListingDataService>("client");
            builder.Services.AddScoped<IFriendsListingDataService, FriendsOfflineListingDataService>();
            builder.Services.AddScoped<IStartChatFormDataService, StartChatClientSideFormDataService>();
            builder.Services.AddScoped<IChatThreadsListingDataService, ChatThreadsOfflineListingDataService>();
            builder.Services.AddKeyedScoped<IChatThreadsListingDataService, ChatThreadsClientSideListingDataService>("client");
            builder.Services.AddScoped<IChatMessagesListingDataService, ChatMessagesClientSideListingDataService>();
            builder.Services.AddScoped<IChatMessageFormDataService, ChatMessageClientSideFormDataService>();
            builder.Services.AddScoped<IChatThreadSyncFormDataService, ChatThreadSyncClientSideFormDataService>();
            builder.Services.AddScoped<IChatMessagesSyncFormDataService, ChatMessagesSyncClientSideFormDataService>();
            builder.Services.AddScoped<IDeviceTokenFormDataService, DeviceTokenClientSideFormDataService>();
            builder.Services.AddScoped<IAuthCodeListingDataService, AuthCodeClientSideListingDataService>();
            builder.Services.AddScoped<IAuthCodeFormDataService, AuthCodeClientSideFormDataService>();

            builder.Services.AddScoped<IAuthenticatedUser, MauiAuthenticatedUser>();
            builder.Services.AddSingleton(_ => CrossFirebaseCloudMessaging.Current);

            // Register error handling services
            builder.Services.AddSingleton<Platform.Client.Services.Features.ErrorHandling.IGlobalExceptionHandler, Platform.Client.Services.Features.ErrorHandling.GlobalExceptionHandler>();

            var app = builder.Build();
            var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<AppDbContext>();
            dbContext.Database.Migrate();
            services.GetRequiredService<ChatSyncUpService>().Start();
            services.GetRequiredService<SignalRClientService>().Start(ChatHubUrl);

            return app;
        }

        private static MauiAppBuilder RegisterFirebaseServices(this MauiAppBuilder builder)
        {
            builder.ConfigureLifecycleEvents(events =>
            {
#if IOS
            events.AddiOS(iOS => iOS.WillFinishLaunching((_,__) => {
                CrossFirebase.Initialize();
                return false;
            }));
#elif ANDROID

                events.AddAndroid(android => android.OnCreate((activity, _) =>
                    CrossFirebase.Initialize(activity)));
                //CrossFirebaseCrashlytics.Current.SetCrashlyticsCollectionEnabled(true);

#endif
            });
            CrossFirebaseCloudMessaging.Current.CheckIfValidAsync();
            //builder.Services.AddSingleton(_ => CrossFirebaseAuth.Current);
            CrossFirebaseCloudMessaging.Current.NotificationReceived += Current_NotificationReceived;
            return builder;
        }

        private static void Current_NotificationReceived(object? sender, Plugin.Firebase.CloudMessaging.EventArgs.FCMNotificationReceivedEventArgs e)
        {

        }

        public static HttpClientHandler GetInsecureHandler()
        {
            HttpClientHandler handler = new HttpClientHandler();
            handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
            {
                if (cert.Issuer.Equals("CN=localhost"))
                    return true;
                return errors == System.Net.Security.SslPolicyErrors.None;
            };
            return handler;
        }
    }
}
