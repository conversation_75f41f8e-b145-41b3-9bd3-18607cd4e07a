﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace DeepMessage.Server.DataServices.Features.Profile;

public class AvatarSelectionServerSideFormDataService : IAvatarSelectionFormDataService
{
    private readonly AppDbContext _context;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<AvatarSelectionServerSideFormDataService> _logger;

    public AvatarSelectionServerSideFormDataService(
        AppDbContext context,
        UserManager<ApplicationUser> userManager,
        ILogger<AvatarSelectionServerSideFormDataService> logger)
    {
        _context = context;
        _userManager = userManager;
        _logger = logger;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(AvatarSelectionFormBusinessObject formBusinessObject)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(formBusinessObject.SelectedImageId, "Selected image ID is required");
            ArgumentNullException.ThrowIfNull(formBusinessObject.TargetId, "Target ID is required");

            // Get the selected image
            var selectedImage = await _context.Images
                .FirstOrDefaultAsync(i => i.Id == formBusinessObject.SelectedImageId);

            if (selectedImage == null)
            {
                throw new ArgumentException($"Image with ID {formBusinessObject.SelectedImageId} not found");
            }
             

            if (formBusinessObject.Context == "profile")
            {
                return await SaveProfileAvatar(formBusinessObject.TargetId, selectedImage.ImageContent);
            }
            else if (formBusinessObject.Context == "friend")
            {
                return await SaveFriendAvatar(formBusinessObject.TargetId, selectedImage.ImageContent);
            }
            else
            {
                throw new ArgumentException($"Unknown context: {formBusinessObject.Context}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving avatar selection for context {Context}, target {TargetId}",
                formBusinessObject.Context, formBusinessObject.TargetId);
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<AvatarSelectionFormBusinessObject> GetItemByIdAsync(string id)
    {
        try
        {
            // Get all available avatar images
            var availableImages = await _context.Images
                .Where(i => i.ImageType == "avatar")
                .OrderBy(i => i.CreatedAt)
                .Select(i => new AvailableImage
                {
                    Id = i.Id,
                    ImageContent = i.ImageContent,
                    ImageType = i.ImageType,
                    CreatedAt = i.CreatedAt
                })
                .ToListAsync();

            return new AvatarSelectionFormBusinessObject
            {
                TargetId = id,
                Context = "profile", // Default context
                AvailableImages = availableImages
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving avatar selection data for ID {Id}", id);
            throw;
        }
    }

    private async Task<string> SaveProfileAvatar(string userId, string avatarData)
    {
        var user = await _userManager.FindByIdAsync(userId);
        if (user == null)
        {
            throw new ArgumentException($"User with ID {userId} not found");
        }

        user.AvatarData = avatarData;
        var result = await _userManager.UpdateAsync(user);

        if (!result.Succeeded)
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            throw new InvalidOperationException($"Failed to update user avatar: {errors}");
        }

        _logger.LogInformation("Profile avatar updated for user {UserId}", userId);
        return JsonSerializer.Serialize(new { success = true, message = "Profile avatar updated successfully" });
    }

    private async Task<string> SaveFriendAvatar(string friendshipId, string imageContent)
    {
        var friendship = await _context.Friendships
            .FirstOrDefaultAsync(f => f.Id == friendshipId);

        if (friendship == null)
        {
            throw new ArgumentException($"Friendship with ID {friendshipId} not found");
        }

        friendship.DisplayPictureUrl = $"data:image/png;base64,{imageContent}";
        await _context.SaveChangesAsync();

        _logger.LogInformation("Friend avatar updated for friendship {FriendshipId}", friendshipId);
        return JsonSerializer.Serialize(new { success = true, message = "Friend avatar updated successfully" });
    }
}
