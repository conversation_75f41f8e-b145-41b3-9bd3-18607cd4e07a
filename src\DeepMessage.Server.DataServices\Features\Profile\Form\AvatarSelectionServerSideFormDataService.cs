﻿using DeepMessage.Framework.Core;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.Framework.Enums;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.DataServices.Features.Profile;
public class AvatarSelectionServerSideFormDataService : IAvatarSelectionFormDataService
{

	private readonly AppDbContext _context;

	public AvatarSelectionServerSideFormDataService (AppDbContext context)
	{
		_context = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AvatarSelectionFormBusinessObject formBusinessObject)
	{
		throw new NotImplementedException();
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AvatarSelectionFormBusinessObject?> GetItemByIdAsync(string id)
	{
		throw new NotImplementedException();
	}
}
