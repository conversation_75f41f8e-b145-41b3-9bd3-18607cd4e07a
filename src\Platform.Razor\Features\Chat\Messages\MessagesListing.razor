@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Conversation
@using DeepMessage.ServiceContracts.Enums
@using Platform.Client.Services.Features.Conversation
@using System.Linq
@using Platform.Razor.Components
@using Platform.Razor.Components.ErrorBoundary
@page "/chat/{ConversationId}"
@inherits ListingBase<ChatMessagesListingViewModel, ChatMessagesListingBusinessObject, ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService>
@layout Platform.Razor.Components.EmptyLayout

<!-- Main Chat Container - WhatsApp Style with Error Boundary -->
<ComponentErrorBoundary ComponentName="MessagesListing"
                        ErrorType="ErrorBoundaryErrorType.Chat"
                        OnRetry="HandleRetry"
                        ContainerClasses="">
    <div class="flex flex-col h-screen">
        <!-- Chat Header - White Theme -->
        <div class="bg-header border-b border-border px-4 py-3 shadow-theme-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <!-- Back Button -->
                    <button @onclick="GoBack"
                            class="p-2 text-primary rounded-full hover:bg-muted transition-theme focus-theme">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                    </button>

                    <!-- Participant Info -->
                    <div class="flex items-center space-x-3">
                        @if (!string.IsNullOrEmpty(ChatIcon))
                        {
                            <img src="@ChatIcon" alt="@ChatTitle"
                                 class="w-10 h-10 rounded-full object-cover" />
                        }
                        else
                        {
                            <div class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-primary font-medium text-lg">@GetInitials(ChatTitle)</span>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(ChatTitle))
                        {
                            <div>
                                <h1 class="text-lg font-semibold text-primary">@ChatTitle</h1>
                                <p class="text-sm text-secondary">@Tagline</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center space-x-1">
                    <button @onclick="ToggleSearch"
                            class="p-2 text-secondary hover:text-primary hover:bg-muted rounded-full transition-theme focus-theme">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    <button class="p-2 text-secondary hover:text-primary hover:bg-muted rounded-full transition-theme focus-theme">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Search Bar (Hidden by default) -->
            @if (showSearch)
            {
                <div class="mt-3 relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-4 w-4 text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text"
                           @bind="FilterViewModel.SearchText"
                           @bind:event="oninput"
                           @onkeyup="OnSearchKeyUp"
                           placeholder="Search messages..."
                           class="w-full pl-10 pr-3 py-2 bg-surface border border-border rounded-lg text-primary placeholder:text-tertiary focus-theme text-sm transition-theme" />
                </div>
            }
        </div>

        <!-- Scroll Date Indicator -->
        @if (showScrollIndicator)
        {
            <div class="scroll-date-indicator @(showScrollIndicator ? "show" : "hide")">
                @scrollIndicatorText
            </div>
        }

        <!-- Messages Container - Sleek Minimal Design with SVG Background -->
        <div class="chat-container-sleek flex flex-col-reverse h-screen w-full flex-1 overflow-y-auto"
             @ref="messagesDiv"
             @onscroll="OnScroll"
             style="scroll-behavior: smooth;"
             data-sleek-scroll="true">
            @if (IsWorking && Items?.Count == 0)
            {
                <!-- Loading State -->
                <div class="flex items-center justify-center py-16 w-full fade-in delay-200">
                    <div class="flex items-center space-x-3 w-full">
                        <div class="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-gray-600"></div>
                        <span class="text-secondary">Loading messages...</span>
                    </div>
                </div>
            }
            else if (!string.IsNullOrEmpty(Error))
            {
                <!-- Error State -->
                <div class="p-4 w-full">
                    <div class="bg-critical-red-50 dark:bg-critical-red-900/20 border border-critical-red-200 dark:border-critical-red-800 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-critical-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-critical-red-800 dark:text-critical-red-200">Error loading messages</h3>
                                <p class="mt-1 text-sm text-critical-red-700 dark:text-critical-red-300">@Error</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <!-- Loading indicator for older messages (appears at top when scrolling up) -->
                @if (IsWorking && Items?.Count > 0)
                {
                    <div class="flex items-center justify-center py-4">
                        <div class="flex items-center space-x-2">
                            <div class="loading-spinner loading-spinner-sm"></div>
                            <span class="text-secondary text-sm">Loading older messages...</span>
                        </div>
                    </div>
                }

                <!-- Messages List - Sleek Minimal Design (Reversed for bottom-up display) -->
                @for (int i = 0; i < Items.Count; i++)
                {
                    var message = Items[i];
                    var previousMessage = i > 0 ? Items[i - 1] : null;

                    <!-- Date Separator -->
                    @if (ShouldShowDateSeparator(message.Timestamp, previousMessage?.Timestamp))
                    {
                        <div class="date-separator hidden">
                            <div class="date-separator-line"></div>
                            <span class="date-separator-text">
                                @GetDateSeparatorText(message.Timestamp ?? DateTime.UtcNow)
                            </span>
                            <div class="date-separator-line"></div>
                        </div>
                    }
                    @* @if (HasImageAttachments(message))
                    {
                        <!-- Picture Message -->
                        <PictureMessageBubble Message="message" OnImageClick="ShowFullImage" />
                    } *@
                    @if (!message.IsIncoming)
                    {
                        <!-- Outgoing Text Message (Right side) - Sleek Minimal -->
                        <div class="message-container-sent">
                            <div class="message-bubble-sent">
                                @message.Content
                            </div>
                            <div class="flex items-center justify-end">
                                <span class="message-timestamp">
                                    @GetFormattedTime(message.Timestamp)
                                </span>
                                @if (message.DeliveryStatus == DeliveryStatus.DeliveredToEndUser)
                                {
                                    <div class="message-status">
                                        <div class="message-status-dot"></div>
                                        <div class="message-status-dot ml-1"></div>
                                    </div>
                                }
                                else
                                {
                                    <div class="message-status">
                                        <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Incoming Text Message (Left side) - Sleek Minimal -->
                        <div class="message-container-received">
                            <div class="message-bubble-received">
                                @message.Content
                            </div>
                            <div class="flex items-center justify-start">
                                <span class="message-timestamp">
                                    @GetFormattedTime(message.Timestamp)
                                </span>
                            </div>
                        </div>
                    }
                }
            }
        </div>

        <!-- Message Input Form with Error Boundary -->
        <div class="bg-surface border-t border-border p-4 shadow-theme-sm">
            <ComponentErrorBoundary ComponentName="MessageForm"
                                    ErrorType="ErrorBoundaryErrorType.Chat"
                                    ContainerClasses=""
                                    ShowDismiss="true">
                <MessageForm ConversationId="@ConversationId" OnMessageSent="OnMessageSent" />
            </ComponentErrorBoundary>
        </div>
    </div>
</ComponentErrorBoundary>

<ComponentErrorBoundary ComponentName="ImageViewer"
                        ErrorType="ErrorBoundaryErrorType.Media"
                        ContainerClasses="fixed inset-0 z-50">
    <ImageViewer IsVisible="@showImageViewer"
                 Attachments="@currentImageAttachments"
                 InitialIndex="@currentImageIndex"
                 OnClose="CloseImageViewer" />
</ComponentErrorBoundary>

