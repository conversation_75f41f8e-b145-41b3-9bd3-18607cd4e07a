﻿using System.Text;
using System.Text.Json;
using System.Net.Http.Json;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts; 
using DeepMessage.ServiceContracts.Features.Profile;
namespace Platform.Client.Services.Features.Profile;
public class AvatarSelectionClientSideFormDataService : IAvatarSelectionFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public AvatarSelectionClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<string> SaveAsync(AvatarSelectionFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/AvatarSelectionsForm/Save", formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	public async Task<AvatarSelectionFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<AvatarSelectionFormBusinessObject>($"api/AvatarSelectionsForm/GetItemById?id=" + id);
	}
}
