# Avatar Generation Feature Setup Guide

## Overview
This document provides setup instructions for the Avatar Generation feature implemented using Azure OpenAI and following the vertical slice architecture pattern.

## Prerequisites

### 1. Azure OpenAI Service
- Azure subscription with access to Azure OpenAI Service
- DALL-E 3 deployment in your Azure OpenAI resource
- API endpoint and key

### 2. Database Migration
You mentioned you'll handle migrations yourself. The following columns need to be added:

**SQL Server (AspNetUsers table):**
```sql
ALTER TABLE AspNetUsers ADD AvatarData VARBINARY(MAX) NULL;
```

**SQLite (Users table):**
```sql
ALTER TABLE Users ADD AvatarData BLOB NULL;
```

## Configuration

### 1. Azure OpenAI Configuration
Update the following configuration files with your Azure OpenAI details:

**appsettings.json:**
```json
{
  "AzureOpenAI": {
    "Endpoint": "https://your-openai-resource.openai.azure.com/",
    "ApiKey": "your-azure-openai-api-key",
    "ImageDeploymentName": "dall-e-3"
  }
}
```

**appsettings.Development.json:**
```json
{
  "AzureOpenAI": {
    "Endpoint": "https://your-openai-resource.openai.azure.com/",
    "ApiKey": "your-azure-openai-api-key",
    "ImageDeploymentName": "dall-e-3"
  }
}
```

### 2. Environment Variables (Recommended for Production)
For production environments, use environment variables or Azure Key Vault:

```bash
AZUREOPENAI__ENDPOINT=https://your-openai-resource.openai.azure.com/
AZUREOPENAI__APIKEY=your-azure-openai-api-key
AZUREOPENAI__IMAGEDEPLOYMENTNAME=dall-e-3
```

## Implementation Details

### Vertical Slice Architecture Components

#### 1. Service Interface
- **IAvatarFormDataService**: Core interface following framework patterns
- Located: `src/DeepMessage.ServiceContracts/Features/Profile/Form/`

#### 2. Server-Side Implementation
- **AvatarServerSideFormDataService**: Azure OpenAI integration
- Located: `src/DeepMessage.Server.DataServices/Features/Profile/Form/`
- Features:
  - Azure OpenAI DALL-E 3 integration
  - Image generation with enhanced prompts
  - Database storage of generated avatars
  - Comprehensive error handling and logging

#### 3. Client-Side Implementation
- **AvatarClientSideFormDataService**: HTTP client for API calls
- Located: `src/Platform.Client.Services/Features/Profile/Form/`

#### 4. Controller
- **AvatarsFormController**: RESTful API following "one service per controller" rule
- Located: `src/DeepMessage.Server.WebApis/Controllers/Profile/`

#### 5. UI Components
- **AvatarForm.razor**: Mobile-first form with Nothing Phone aesthetic
- **AvatarDisplay.razor**: Reusable avatar display component
- Located: `src/Platform.Razor/Features/Profile/Form/` and `src/Platform.Razor/Components/Avatar/`

### Dependency Registration

#### Server Application
```csharp
builder.Services.AddScoped<IAvatarFormDataService, AvatarServerSideFormDataService>();
```

#### Client Applications
```csharp
builder.Services.AddScoped<IAvatarFormDataService, AvatarClientSideFormDataService>();
```

## Usage

### 1. Avatar Generation Form
- Navigate to Profile Management
- Click "Generate Avatar" button
- Describe desired avatar appearance
- Submit form to generate AI avatar

### 2. Avatar Display
- Generated avatars are stored in the database
- Displayed in user profiles and friend lists
- Fallback to initials if no avatar exists

### 3. Integration Points
- Profile management page
- Friend listings
- Chat interfaces
- Any component using the AvatarDisplay component

## API Endpoints

### Generate Avatar
```
POST /api/AvatarsForm/SaveAsync
Content-Type: application/json

{
  "id": "user-id",
  "avatarDescription": "A professional woman with short brown hair, wearing a blue blazer, smiling confidently"
}
```

### Get Avatar Data
```
GET /api/AvatarsForm/GetItemByIdAsync?id=user-id
```

## Error Handling

### Common Issues
1. **Azure OpenAI Configuration Missing**: Check appsettings.json configuration
2. **API Key Invalid**: Verify Azure OpenAI API key and permissions
3. **Deployment Not Found**: Ensure DALL-E 3 deployment exists and name matches configuration
4. **Database Migration**: Ensure AvatarData column exists in both databases

### Logging
- All avatar generation attempts are logged
- Error details captured for troubleshooting
- Success metrics tracked

## Security Considerations

### 1. API Key Protection
- Store API keys in environment variables or Azure Key Vault
- Never commit API keys to source control
- Use different keys for development and production

### 2. Content Filtering
- Azure OpenAI includes built-in content filtering
- Additional validation can be added to avatar descriptions
- Consider implementing rate limiting for avatar generation

### 3. Data Privacy
- Avatar data stored as binary in database
- No external storage of generated images
- User descriptions not permanently stored

## Testing

### 1. Unit Tests
- Test service logic without Azure OpenAI calls
- Mock Azure OpenAI client for testing
- Validate error handling scenarios

### 2. Integration Tests
- Test full avatar generation flow
- Verify database storage
- Test API endpoints

### 3. Manual Testing
- Test various avatar descriptions
- Verify UI responsiveness
- Test error scenarios (invalid API key, network issues)

## Performance Considerations

### 1. Generation Time
- Avatar generation typically takes 10-30 seconds
- UI shows loading states during generation
- Consider implementing timeout handling

### 2. Storage
- Generated images are typically 1-5MB
- Monitor database storage growth
- Consider implementing cleanup for old avatars

### 3. Rate Limiting
- Azure OpenAI has rate limits
- Implement client-side rate limiting if needed
- Consider caching generated avatars

## Troubleshooting

### Common Error Messages
1. **"Azure OpenAI configuration is missing"**: Check appsettings.json
2. **"Failed to generate avatar using AI service"**: Check API key and endpoint
3. **"User not found"**: Ensure user is authenticated
4. **"Avatar description is required"**: Validate form input

### Debug Steps
1. Check application logs for detailed error messages
2. Verify Azure OpenAI service status
3. Test API key with Azure OpenAI Studio
4. Validate database connection and schema

## Future Enhancements

### Potential Improvements
1. **Avatar Styles**: Support for different art styles (cartoon, realistic, etc.)
2. **Batch Generation**: Generate multiple avatar options
3. **Avatar History**: Store multiple avatars per user
4. **Custom Prompts**: Allow advanced users to customize prompts
5. **Image Editing**: Basic editing capabilities for generated avatars

### Framework Enhancements
1. **Offline Support**: Cache generated avatars for offline use
2. **Sync Service**: Synchronize avatars across devices
3. **Background Generation**: Queue avatar generation for better UX
