@page "/profile"
@using Platform.Framework.Core
@using Platform.Razor.Components.ImageUpload

<!-- Profile Management - Sleek Minimal Design -->
<div class="h-full bg-background">
    <!-- Header -->
    <div class="nav-header">
        <div class="px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-responsive-xl font-semibold text-primary">My Profile</h1>
                <button @onclick="SaveProfile"
                        disabled="@IsSaving"
                        class="btn-primary btn-sm">
                    @if (IsSaving)
                    {
                        <div class="loading-spinner loading-spinner-sm mr-2"></div>
                        <span>Saving...</span>
                    }
                    else
                    {
                        <span>Save</span>
                    }
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="flex-1 overflow-y-auto mobile-content">
        <!-- Profile Picture Section -->
        <div class="bg-surface border-b border-border touch-spacing">
            <div class="flex flex-col items-center">
                @* <h2 class="text-responsive-lg font-semibold text-primary mb-4">Profile Picture</h2> *@
                 
                <ImageUploadComponent CurrentImageUrl="@profilePictureUrl"
                                    PlaceholderText="@displayName"
                                    AltText="Profile picture"
                                    Size="ImageSize.Medium"
                                    HelpText="Upload your profile picture. Supported formats: JPG, PNG, WebP. Max size: 2MB"
                                    OnImageUploaded="OnProfilePictureUploaded"
                                    OnUploadError="OnProfilePictureError" />
            </div>
        </div>

        <!-- Nickname Section -->
        <div class="bg-surface border-b border-border mt-2">
            <div class="touch-spacing">
                <div class="form-group">
                    <label for="displayName" class="form-label">
                        Display Name
                    </label>
                    <div class="relative">
                        <input type="text" @bind="displayName" id="displayName"
                               placeholder="Enter your display name"
                               class="form-control pr-10" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center" aria-hidden="true">
                            <svg class="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                    </div>
                    <p class="form-help">This name will be visible to your friends when you add them as your friend, they can change your name in their contact list also</p>
                </div>
            </div>
        </div>

        <!-- Password Change Section -->
        <div class="bg-surface border-b border-border mt-4">
            <div class="touch-spacing"> 

                <!-- Change Password -->
                <div @onclick="ChangePassword"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Change Password</h4>
                        <p class="text-sm text-secondary">Update your account password</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Referral Codes Section -->
        <div class="bg-surface mt-4">
            <div class="touch-spacing">
          @*       <h3 class="text-responsive-lg font-semibold text-primary mb-4">Referral Codes</h3> *@

                <!-- Referral Codes -->
                <div @onclick="NavigateToReferralCodes"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Manage Referral Codes</h4>
                        <p class="text-sm text-secondary">Create and share friend invitation codes</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
