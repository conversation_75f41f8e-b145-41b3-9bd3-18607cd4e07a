@page "/profile"
@using Platform.Framework.Core
@using Platform.Razor.Components.ImageUpload
@using Platform.Razor.Features.Profile.Form
@using Platform.Razor.Components.Avatar
@using System.Security.Claims
@inherits Platform.Framework.Core.FrameworkBaseComponent

<!-- Profile Management - Sleek Minimal Design -->
<div class="h-full bg-background">
    <!-- Header -->
    <div class="nav-header">
        <div class="px-4 py-4">
            <div class="flex items-center justify-between">
                <h1 class="text-responsive-xl font-semibold text-primary">My Profile</h1>
                <button @onclick="SaveProfile"
                        disabled="@IsSaving"
                        class="btn-primary btn-sm">
                    @if (IsSaving)
                    {
                        <div class="loading-spinner loading-spinner-sm mr-2"></div>
                        <span>Saving...</span>
                    }
                    else
                    {
                        <span>Save</span>
                    }
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="flex-1 overflow-y-auto mobile-content">
        <!-- Profile Picture Section -->
        <div class="bg-surface border-b border-border touch-spacing">
            <div class="flex flex-col items-center space-y-4">
                @* <h2 class="text-responsive-lg font-semibold text-primary mb-4">Profile Picture</h2> *@

                <!-- Current Avatar Display -->
                @if (currentUserAvatarData != null)
                {
                    <div class="text-center">
                        <h3 class="text-sm font-medium text-primary mb-2">Current Avatar</h3>
                        <AvatarDisplay AvatarData="@currentUserAvatarData"
                                     DisplayName="@displayName"
                                     Size="AvatarDisplay.AvatarSize.ExtraLarge"
                                     IsClickable="false" />
                    </div>
                }

                <ImageUploadComponent CurrentImageUrl="@profilePictureUrl"
                                    PlaceholderText="@displayName"
                                    AltText="Profile picture"
                                    Size="ImageSize.Medium"
                                    HelpText="Upload your profile picture. Supported formats: JPG, PNG, WebP. Max size: 2MB"
                                    OnImageUploaded="OnProfilePictureUploaded"
                                    OnUploadError="OnProfilePictureError" />
            </div>
        </div>

        <!-- Avatar Generation Section -->
        <div class="bg-surface border-b border-border mt-2">
            <div class="touch-spacing">
                <!-- Generate Avatar -->
                <div @onclick="OpenAvatarGenerator"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Generate AI Avatar</h4>
                        <p class="text-sm text-secondary">Create a personalized avatar using AI</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>

                <!-- Select Avatar -->
                <div @onclick="OpenAvatarSelection"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Select Avatar</h4>
                        <p class="text-sm text-secondary">Choose from our collection of avatars</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Nickname Section -->
        <div class="bg-surface border-b border-border mt-2">
            <div class="touch-spacing">
                <div class="form-group">
                    <label for="displayName" class="form-label">
                        Display Name
                    </label>
                    <div class="relative">
                        <input type="text" @bind="displayName" id="displayName"
                               placeholder="Enter your display name"
                               class="form-control pr-10" />
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center" aria-hidden="true">
                            <svg class="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                    </div>
                    <p class="form-help">This name will be visible to your friends when you add them as your friend, they can change your name in their contact list also</p>
                </div>
            </div>
        </div>

        <!-- Password Change Section -->
        <div class="bg-surface border-b border-border mt-4">
            <div class="touch-spacing"> 

                <!-- Change Password -->
                <div @onclick="ChangePassword"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-6 6h-2.5a2.5 2.5 0 01-2.5-2.5 2.5 2.5 0 012.5-2.5H16a2 2 0 002-2m-7 4v4m0 0H9m2 0h2m-2-4a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Change Password</h4>
                        <p class="text-sm text-secondary">Update your account password</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Referral Codes Section -->
        <div class="bg-surface mt-4">
            <div class="touch-spacing">
          @*       <h3 class="text-responsive-lg font-semibold text-primary mb-4">Referral Codes</h3> *@

                <!-- Referral Codes -->
                <div @onclick="NavigateToReferralCodes"
                     class="flex items-center py-3 cursor-pointer hover:bg-muted -mx-4 px-4 rounded-lg transition-theme touch-target">
                    <div class="flex-shrink-0 mr-3">
                        <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-primary">Manage Referral Codes</h4>
                        <p class="text-sm text-secondary">Create and share friend invitation codes</p>
                    </div>
                    <svg class="w-5 h-5 text-secondary ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private void OpenAvatarGenerator()
    {
        ShowDialog<AvatarForm>("Generate Avatar", null, Size.Md, Position_.Center, true);
    }

    private async Task OpenAvatarSelection()
    {
        var userId = await StorageService.GetValue(ClaimTypes.NameIdentifier);
        if (!string.IsNullOrEmpty(userId))
        {
            ShowDialog<AvatarSelection>("Select Avatar", new Dictionary<string, object>
            {
                { "Context", "profile" },
                { "TargetId", userId }
            }, Size.Md, Position_.Center, true);
        }
    }
}
