@using DeepMessage.Framework.Core
@using DeepMessage.ServiceContracts.Features.Account
@using ModelFury.Briefly.MobileApp.Features.Account
@using Platform.Framework.Core
@page "/captcha"
@page "/verify"
@inherits FormBase<SigninFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>

<!-- Main Container with Fixed Dimensions - Nothing Phone Theme -->
<div class="p-4  bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <!-- Fixed Height Container to Prevent Layout Shifts -->
    <div class="h-64 flex flex-col">

        @if (IsWorking)
        {
            <!-- Loading State with Shimmer Effect -->
            <div class="flex-1 flex flex-col justify-center items-center space-y-4 w-full">
                <div class="animate-pulse">
                    <!-- Shimmer for header area -->
                    <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-4"></div>
                    <!-- Shimmer for captcha area -->
                    <div class="h-20 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
                    <!-- Shimmer for input area -->
                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
                    <!-- Shimmer for button -->
                    <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                </div>
            </div>
        }
        else
        {
            <!-- Header Section - Fixed Height -->
            <div class="h-16 mb-4 flex flex-col justify-center">
                @if (string.IsNullOrEmpty(NickName))
                {
                    <!-- Activation Message -->
                    <div class="border border-gray-300 dark:border-gray-600 rounded p-3 bg-gray-50 dark:bg-gray-800 text-center">
                        <span class="text-sm text-gray-700 dark:text-gray-300">
                            Activate Application using Purchase or Referral code
                        </span>
                    </div>
                }
                else
                {
                    <!-- App Activation Status -->
                    <div class="text-center space-y-1">
                        <div class="text-xs text-gray-600 dark:text-gray-400">
                            Activated for <span class="font-mono text-gray-800 dark:text-gray-200">@ObfuscateUsername(NickName)</span> - 

                            <span @onclick="ResetRegistration"
                                  class="text-xs text-blue-600 dark:text-blue-400 underline cursor-pointer hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                Reset registration
                            </span>
                        </div>
                        
                    </div>
                }
            </div>

            <!-- Captcha Section - Fixed Height -->
            <div class="h-24 mb-4 flex flex-col justify-center">
                @if (!string.IsNullOrEmpty(NickName))
                {
                    <!-- Captcha Display -->
                    <div class="border border-gray-300 dark:border-gray-600 rounded p-2 bg-gray-50 dark:bg-gray-800">
                        <div class="flex justify-center mb-2">
                            <img src="images/c1.jpg" class="h-8 w-auto" alt="Captcha" />
                        </div>
                        <div class="flex justify-between text-xs text-blue-600 dark:text-blue-400">
                            <button type="button" class="hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                ↻ Refresh
                            </button>
                            <button type="button" class="hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                🔊 Audio
                            </button>
                        </div>
                    </div>
                }
                else
                {
                    <!-- Empty space to maintain consistent height -->
                    <div class="h-full"></div>
                }
            </div>

            <!-- Form Section - Fixed Height -->
            <div class="flex-1 flex flex-col">
                <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit" class="flex-1 flex flex-col">
                    <DataAnnotationsValidator />

                    <!-- Verification Field - Fixed Height -->
                    <div class="h-20 mb-4">
                        <label for="authCode" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                            @(string.IsNullOrEmpty(NickName) ? "Activation Code" : "Verification Code")
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m5 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h1m2-4a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2h-4a2 2 0 01-2-2V5z" />
                                </svg>
                            </div>
                            <InputText @bind-Value="SelectedItem!.PassKey"
                                       id="authCode"
                                       type="password"
                                       placeholder="Enter code"
                                       class="block w-full pl-9 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:ring-primary-500 focus:border-primary-500 transition-colors" />
                        </div>
                        <div class="h-5 mt-1">
                            <ValidationMessage For="@(() => SelectedItem!.PassKey)" class="text-xs text-red-600 dark:text-red-400" />
                        </div>
                    </div>

                    <!-- Submit Button - Fixed Height -->
                    <div class="h-10 mb-4">
                        <button type="submit"
                                disabled="@(IsWorking)"
                                class="w-full h-full py-2 text-sm font-medium rounded-full btn-secondary  disabled:bg-primary-400 text-white disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2">
                            @if (IsWorking)
                            {
                                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>Verifying...</span>
                            }
                            else
                            {
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                <span>Verify</span>
                            }
                        </button>
                    </div>

                    <!-- Error Section - Fixed Height -->
                    <div class="h-12 flex items-start">
                        @if (!string.IsNullOrEmpty(Error))
                        {
                            <div class="w-full p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-700 dark:text-red-300">
                                @Error
                            </div>
                        }
                    </div>
                </EditForm>
            </div>
        }
    </div>
</div>
