﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.Client.Common.Data
{
    /// <summary>
    /// Represents the friendship relationship once two users become friends.
    /// You can store blocked/removed states here or keep it minimal.
    /// </summary>
    public class Friendship
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        // Typically, you store each pair of friends in both directions 
        // or enforce a single row for both. 
        // A simple approach is two rows (one for each direction).
        // Alternatively, if you want a single row for a mutual friendship, 
        // you can store (UserAId, UserBId) as a unique pair and handle symmetrical logic.

        public string UserId { get; set; } = null!; 

        public string FriendId { get; set; } = null!;

        [StringLength(450)]
        public string Name { get; set; } = string.Empty;

        [StringLength(450)]
        public string? DisplayPictureUrl { get; set; }

        /// <summary>
        /// Stores the generated avatar image data as byte array
        /// </summary>
        public string? AvatarData { get; set; }


        [StringLength(450)]
        public string? AvatarDescription { get; set; }


        [StringLength(4000)]
        public string Pub1 { get; set; } = string.Empty;

        // If you want to track the date/time they became friends
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public byte SyncStatus { get; set; }

        // If you want to handle block/unblock
        public bool IsBlocked { get; set; } = false;
        public DateTime? BlockedAt { get; set; }

        // Soft delete, in case you want to keep a record
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }
    }

  
}
