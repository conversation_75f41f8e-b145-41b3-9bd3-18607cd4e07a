@inherits FormBase<AvatarSelectionFormBusinessObject, AvatarSelectionFormViewModel, string, IAvatarSelectionFormDataService>
@using DeepMessage.ServiceContracts.Features.Profile
@using Platform.Client.Services.Features.Profile
@using Platform.Framework.Core

<!-- Modal Dialog Content with Nothing Phone Aesthetic -->
<div class="bg-white p-6 max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
            @if (SelectedItem?.Context == "friend")
            {
                <span>Select Friend Avatar</span>
            }
            else
            {
                <span>Select Avatar</span>
            }
        </h2>
        <p class="text-gray-600 text-sm">Choose from our collection of avatars</p>
    </div>

    <!-- Error State -->
    @if (!string.IsNullOrEmpty(Error))
    {
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-red-800 text-sm font-medium">Error</span>
            </div>
            <p class="text-red-700 text-sm mt-1">@Error</p>
        </div>
    }

    <!-- Success State -->
    @if (!string.IsNullOrEmpty(SelectedItem?.SuccessMessage))
    {
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-green-800 text-sm font-medium">Success!</span>
            </div>
            <p class="text-green-700 text-sm mt-1">@SelectedItem.SuccessMessage</p>
        </div>
    }

    <!-- Loading State -->
    @if (SelectedItem?.IsLoadingImages == true || IsWorking)
    {
        <div class="flex flex-col items-center justify-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mb-4"></div>
            <p class="text-gray-600 text-center">
                @if (IsWorking)
                {
                    <span>Saving selection...</span>
                }
                else
                {
                    <span>Loading avatars...</span>
                }
            </p>
        </div>
    }
    else if (SelectedItem?.AvailableImages?.Any() == true)
    {
        <!-- Avatar Grid -->
        <div class="mb-6">
            <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-4">
                @foreach (var image in SelectedItem.AvailableImages)
                {
                    <div class="relative">
                        <button type="button"
                                @onclick="() => SelectImage(image.Id)"
                                class="@GetImageButtonClasses(image.Id)"
                                style="min-height: 44px; min-width: 44px;">
                            <img src="@GetImageDataUrl(image.ImageContent)" 
                                 alt="Avatar option" 
                                 class="w-20 h-20 object-cover rounded-lg" />
                            
                            <!-- Selected State Overlay -->
                            @if (SelectedItem.SelectedImageId == image.Id)
                            {
                                <div class="absolute inset-0 bg-gray-900 bg-opacity-50 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            }
                        </button>
                    </div>
                }
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-3">
            <button type="button"
                    @onclick="CloseDialog"
                    class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors min-h-[44px]">
                Cancel
            </button>
            
            <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
                <DataAnnotationsValidator />
                <button type="submit"
                        class="px-6 py-3 bg-gray-900 text-white rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-900 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors min-h-[44px]"
                        disabled="@(IsWorking || string.IsNullOrEmpty(SelectedItem?.SelectedImageId))">
                    @if (IsWorking)
                    {
                        <span class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </span>
                    }
                    else
                    {
                        <span>Select Avatar</span>
                    }
                </button>
                
                <!-- Validation Summary -->
                <ValidationSummary class="mt-4 text-red-600 text-sm" />
            </EditForm>
        </div>
    }
    else
    {
        <!-- No Images State -->
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No avatars available</h3>
            <p class="text-gray-600">Please try again later or contact support.</p>
        </div>
    }
</div>
