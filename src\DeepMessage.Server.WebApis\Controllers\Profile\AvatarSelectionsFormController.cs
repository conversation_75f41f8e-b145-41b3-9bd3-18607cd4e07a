﻿using Microsoft.AspNetCore.Mvc;
using DeepMessage.Framework.Enums;
using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Features.Profile;
namespace DeepMessage.Server.WebApis.Controller.Profile;
[ApiController, Route("{culture:culture}/api/[controller]/[action]")]
public class AvatarSelectionsFormController : ControllerBase, IAvatarSelectionFormDataService
{

	private readonly IAvatarSelectionFormDataService dataService;

	public AvatarSelectionsFormController(IAvatarSelectionFormDataService dataService)
	{
		this.dataService = dataService;
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] AvatarSelectionFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	[SystemClaim(SystemClaimType.SystemDefault)]
	[HttpGet]
	public async Task<AvatarSelectionFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
