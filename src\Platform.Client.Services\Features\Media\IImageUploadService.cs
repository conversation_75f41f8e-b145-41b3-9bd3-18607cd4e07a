using Microsoft.AspNetCore.Components.Forms;

namespace Platform.Client.Services.Features.Media;

/// <summary>
/// Service for handling image uploads with compression, validation, and processing
/// </summary>
public interface IImageUploadService
{
    /// <summary>
    /// Uploads and processes a profile picture
    /// </summary>
    /// <param name="file">The image file to upload</param>
    /// <param name="maxSizeKB">Maximum file size in KB (default: 2048)</param>
    /// <returns>Upload result with URL and metadata</returns>
    Task<ImageUploadResult> UploadProfilePictureAsync(IBrowserFile file, int maxSizeKB = 2048);

    /// <summary>
    /// Validates image file format and size
    /// </summary>
    /// <param name="file">The file to validate</param>
    /// <param name="maxSizeKB">Maximum size in KB</param>
    /// <returns>Validation result</returns>
    ImageValidationResult ValidateImage(IBrowserFile file, int maxSizeKB = 2048);

    /// <summary>
    /// Compresses and resizes image for optimal display
    /// </summary>
    /// <param name="imageData">Original image data</param>
    /// <param name="maxWidth">Maximum width (default: 400)</param>
    /// <param name="maxHeight">Maximum height (default: 400)</param>
    /// <param name="quality">JPEG quality 1-100 (default: 85)</param>
    /// <returns>Compressed image data</returns>
    Task<byte[]> ProcessImageAsync(byte[] imageData, int maxWidth = 400, int maxHeight = 400, int quality = 85);

    /// <summary>
    /// Generates a thumbnail from image data
    /// </summary>
    /// <param name="imageData">Original image data</param>
    /// <param name="size">Thumbnail size (default: 150)</param>
    /// <returns>Thumbnail image data</returns>
    Task<byte[]> GenerateThumbnailAsync(byte[] imageData, int size = 150);
}

/// <summary>
/// Result of image upload operation
/// </summary>
public class ImageUploadResult
{
    public bool Success { get; set; }
    public string? ImageUrl { get; set; }
    public string? ThumbnailUrl { get; set; }
    public string? ErrorMessage { get; set; }
    public long FileSizeBytes { get; set; }
    public string? FileName { get; set; }
    public string? MimeType { get; set; }
}

/// <summary>
/// Result of image validation
/// </summary>
public class ImageValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Supported image formats
/// </summary>
public static class SupportedImageFormats
{
    public static readonly string[] MimeTypes = {
        "image/jpeg",
        "image/jpg", 
        "image/png",
        "image/webp"
    };

    public static readonly string[] Extensions = {
        ".jpg",
        ".jpeg",
        ".png",
        ".webp"
    };

    public static bool IsSupported(string mimeType)
    {
        return MimeTypes.Contains(mimeType.ToLowerInvariant());
    }

    public static bool IsSupportedExtension(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return Extensions.Contains(extension);
    }
}
