﻿@inherits FormBase<AvatarSelectionFormBusinessObject,AvatarSelectionFormViewModel, string, IAvatarSelectionFormDataService>
@using DeepMessage.ServiceContracts.Features.Profile
@using Platform.Client.Services.Features.Profile

<div class="form-container">
    @if (IsWorking)
    {
        <div class="loading">Loading...</div>
    }
    else if (!string.IsNullOrEmpty(Error))
    {
        <div class="error">@Error</div>
    }
    else
    {
        <EditForm Model="SelectedItem" OnValidSubmit="HandleFormSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />
            
            <!-- Add your form fields here -->
            <div class="form-group">
                <label>Sample Field:</label>
                <InputText @bind-Value="SelectedItem.ToString()" class="form-control" />
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary" disabled="@IsWorking">Save</button>
            </div>
        </EditForm>
    }
</div>
